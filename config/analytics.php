<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Google Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for Google Analytics integration.
    | You can enable/disable analytics, set the measurement ID, and configure
    | various tracking options.
    |
    */

    'google' => [
        /*
        |--------------------------------------------------------------------------
        | Google Analytics Enabled
        |--------------------------------------------------------------------------
        |
        | This option controls whether Google Analytics tracking is enabled.
        | When disabled, no tracking scripts will be loaded or events sent.
        |
        */
        'enabled' => env('GOOGLE_ANALYTICS_ENABLED', false),

        /*
        |--------------------------------------------------------------------------
        | Google Analytics Measurement ID
        |--------------------------------------------------------------------------
        |
        | Your Google Analytics 4 measurement ID (format: G-XXXXXXXXXX).
        | This can be found in your Google Analytics property settings.
        |
        */
        'measurement_id' => env('GOOGLE_ANALYTICS_MEASUREMENT_ID'),

        /*
        |--------------------------------------------------------------------------
        | Debug Mode
        |--------------------------------------------------------------------------
        |
        | When enabled, additional debug information will be logged and
        | the gtag debug mode will be activated for development.
        |
        */
        'debug' => env('GOOGLE_ANALYTICS_DEBUG', false),

        /*
        |--------------------------------------------------------------------------
        | Cookie Settings
        |--------------------------------------------------------------------------
        |
        | Configure cookie behavior for Google Analytics tracking.
        | These settings help with GDPR compliance.
        |
        */
        'cookie_domain' => env('GOOGLE_ANALYTICS_COOKIE_DOMAIN', 'auto'),
        'cookie_expires' => env('GOOGLE_ANALYTICS_COOKIE_EXPIRES', 63072000), // 2 years in seconds
        'anonymize_ip' => env('GOOGLE_ANALYTICS_ANONYMIZE_IP', true),

        /*
        |--------------------------------------------------------------------------
        | Consent Mode
        |--------------------------------------------------------------------------
        |
        | Configure Google Analytics Consent Mode for GDPR compliance.
        | This allows analytics to work with limited data when consent is denied.
        |
        */
        'consent_mode' => [
            'enabled' => env('GOOGLE_ANALYTICS_CONSENT_MODE', true),
            'default_settings' => [
                'ad_storage' => 'denied',
                'analytics_storage' => 'denied',
                'ad_user_data' => 'denied',
                'ad_personalization' => 'denied',
                'functionality_storage' => 'granted',
                'security_storage' => 'granted',
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Custom Dimensions and Metrics
        |--------------------------------------------------------------------------
        |
        | Define custom dimensions and metrics that will be tracked.
        | These can be configured in your Google Analytics property.
        |
        */
        'custom_dimensions' => [
            'user_role' => env('GOOGLE_ANALYTICS_USER_ROLE_DIMENSION'),
            'user_type' => env('GOOGLE_ANALYTICS_USER_TYPE_DIMENSION'),
        ],

        /*
        |--------------------------------------------------------------------------
        | Events Configuration
        |--------------------------------------------------------------------------
        |
        | Configure which events should be automatically tracked.
        |
        */
        'auto_track' => [
            'page_views' => true,
            'file_downloads' => true,
            'external_links' => true,
            'form_submissions' => true,
            'scroll_depth' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Privacy Settings
    |--------------------------------------------------------------------------
    |
    | Configure privacy-related settings for analytics tracking.
    |
    */
    'privacy' => [
        'cookie_consent_required' => env('ANALYTICS_COOKIE_CONSENT_REQUIRED', true),
        'respect_do_not_track' => env('ANALYTICS_RESPECT_DNT', true),
        'data_retention_days' => env('ANALYTICS_DATA_RETENTION_DAYS', 730), // 2 years
    ],

];
