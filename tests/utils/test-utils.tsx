import React, { ReactElement, ReactNode } from 'react';
import { render, RenderOptions, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

// Mock Analytics Provider for testing
const MockAnalyticsProvider = ({ children }: { children: ReactNode }) => {
  return (
    <div data-testid="mock-analytics-provider">
      {children}
    </div>
  );
};

// Create mock functions that can be imported by tests
export const mockTrackPageView = vi.fn();
export const mockTrackEvent = vi.fn();
export const mockTrackCustomEvent = vi.fn();
export const mockTrackEcommerce = vi.fn();
export const mockSetUserProperties = vi.fn();
export const mockUpdateConsent = vi.fn();

// Mock the useAnalytics hook
const mockUseAnalytics = () => ({
  analytics: null,
  isInitialized: false,
  trackPageView: mockTrackPageView,
  trackEvent: mockTrackEvent,
  trackCustomEvent: mockTrackCustomEvent,
  trackEcommerce: mockTrackEcommerce,
  setUserProperties: mockSetUserProperties,
  updateConsent: mockUpdateConsent,
});

// Mock the AnalyticsProvider module
vi.mock('@/components/analytics/AnalyticsProvider', () => ({
  default: MockAnalyticsProvider,
  AnalyticsProvider: MockAnalyticsProvider,
  useAnalytics: mockUseAnalytics,
  usePageTracking: vi.fn(),
  useUserTracking: vi.fn(),
}));

// Test wrapper that includes all necessary providers
const AllTheProviders = ({ children }: { children: ReactNode }) => {
  return (
    <MockAnalyticsProvider>
      {children}
    </MockAnalyticsProvider>
  );
};

// Custom render function that includes common providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return render(ui, {
    wrapper: AllTheProviders,
    ...options,
  });
};

// Dialog testing utilities
export const dialogTestUtils = {
  /**
   * Wait for a dialog to open and return the dialog element
   */
  async waitForDialogToOpen(timeout = 3000): Promise<HTMLElement | null> {
    try {
      await waitFor(() => {
        const dialog = screen.queryByRole('dialog');
        expect(dialog).toBeInTheDocument();
      }, { timeout });
      
      return screen.getByRole('dialog');
    } catch {
      // If dialog doesn't open, return null instead of throwing
      return null;
    }
  },

  /**
   * Wait for a dialog to close
   */
  async waitForDialogToClose(timeout = 3000): Promise<void> {
    await waitFor(() => {
      const dialog = screen.queryByRole('dialog');
      expect(dialog).not.toBeInTheDocument();
    }, { timeout });
  },

  /**
   * Check if a dialog is currently open
   */
  isDialogOpen(): boolean {
    return screen.queryByRole('dialog') !== null;
  },

  /**
   * Get all open dialogs
   */
  getAllOpenDialogs(): HTMLElement[] {
    return screen.queryAllByRole('dialog');
  },

  /**
   * Close dialog by pressing Escape key
   */
  async closeDialogWithEscape(): Promise<void> {
    await userEvent.keyboard('{Escape}');
  },

  /**
   * Close dialog by clicking overlay (if it exists)
   */
  async closeDialogWithOverlay(): Promise<void> {
    const overlay = document.querySelector('[data-slot="dialog-overlay"]');
    if (overlay) {
      await userEvent.click(overlay as HTMLElement);
    }
  }
};

// Portal cleanup utilities
export const portalTestUtils = {
  /**
   * Clean up all portals and their DOM nodes - React-friendly approach
   */
  cleanupPortals(): void {
    // Only clean up body styles and attributes, let React handle DOM nodes
    document.body.style.removeProperty('pointer-events');
    document.body.removeAttribute('data-scroll-locked');
    document.body.style.removeProperty('overflow');

    // Clean up any remaining event listeners on document
    const events = ['keydown', 'keyup', 'click', 'mousedown', 'mouseup'];
    events.forEach(eventType => {
      // Clone the document to remove all event listeners
      // This is a safe way to clean up without interfering with React
      const clonedDoc = document.cloneNode(false);
      // We can't actually replace the document, so we'll just reset body styles
    });

    // Reset any global state that might interfere with tests
    if (typeof window !== 'undefined') {
      // Clear any timeouts that might be running
      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }
    }
  },

  /**
   * Setup portal cleanup for a test
   */
  setupPortalCleanup(): void {
    afterEach(() => {
      // Don't wrap in act() to avoid interfering with React's cleanup
      portalTestUtils.cleanupPortals();
    });
  },

  /**
   * Safe cleanup that waits for React to finish
   */
  async safeCleanup(): Promise<void> {
    // Wait for any pending React updates
    await new Promise(resolve => setTimeout(resolve, 0));

    // Only clean up what React doesn't handle
    document.body.style.removeProperty('pointer-events');
    document.body.removeAttribute('data-scroll-locked');
    document.body.style.removeProperty('overflow');
  }
};

// Component testing utilities
export const componentTestUtils = {
  /**
   * Wait for component to be fully loaded and rendered
   */
  async waitForComponentToLoad(testId: string, timeout = 3000): Promise<HTMLElement> {
    return await waitFor(() => {
      const element = screen.getByTestId(testId);
      expect(element).toBeInTheDocument();
      return element;
    }, { timeout });
  },

  /**
   * Wait for loading state to finish
   */
  async waitForLoadingToFinish(timeout = 3000): Promise<void> {
    await waitFor(() => {
      const loadingElements = screen.queryAllByText(/loading/i);
      expect(loadingElements).toHaveLength(0);
    }, { timeout });
  },

  /**
   * Wait for error state to appear
   */
  async waitForErrorToAppear(errorText: string, timeout = 3000): Promise<HTMLElement> {
    return await waitFor(() => {
      const errorElement = screen.getByText(errorText);
      expect(errorElement).toBeInTheDocument();
      return errorElement;
    }, { timeout });
  },

  /**
   * Simulate keyboard shortcut
   */
  async simulateKeyboardShortcut(key: string, modifiers: { ctrl?: boolean; meta?: boolean; shift?: boolean; alt?: boolean } = {}): Promise<void> {
    const keyEvent = {
      key,
      ctrlKey: modifiers.ctrl || false,
      metaKey: modifiers.meta || false,
      shiftKey: modifiers.shift || false,
      altKey: modifiers.alt || false,
      bubbles: true,
      cancelable: true,
    };

    await act(async () => {
      document.dispatchEvent(new KeyboardEvent('keydown', keyEvent));
    });
  }
};

// Form testing utilities
export const formTestUtils = {
  /**
   * Fill form field by label
   */
  async fillFieldByLabel(label: string, value: string): Promise<void> {
    const field = screen.getByLabelText(label);
    await userEvent.clear(field);
    await userEvent.type(field, value);
  },

  /**
   * Fill form field by placeholder
   */
  async fillFieldByPlaceholder(placeholder: string, value: string): Promise<void> {
    const field = screen.getByPlaceholderText(placeholder);
    await userEvent.clear(field);
    await userEvent.type(field, value);
  },

  /**
   * Submit form by button text
   */
  async submitFormByButtonText(buttonText: string): Promise<void> {
    const button = screen.getByRole('button', { name: buttonText });
    await userEvent.click(button);
  },

  /**
   * Toggle switch by label
   */
  async toggleSwitchByLabel(label: string): Promise<void> {
    const switchElement = screen.getByLabelText(label);
    await userEvent.click(switchElement);
  }
};

// Mock utilities
export const mockUtils = {
  /**
   * Get mock analytics functions for testing
   */
  getMockAnalytics() {
    return mockUseAnalytics();
  },

  /**
   * Create a mock router for Inertia.js
   */
  createMockRouter() {
    return {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
      delete: vi.fn(),
      reload: vi.fn(),
      visit: vi.fn(),
    };
  },

  /**
   * Create a mock useForm hook
   */
  createMockUseForm(initialData: any = {}) {
    const mockSetData = vi.fn();
    const mockPost = vi.fn();
    const mockGet = vi.fn();
    const mockPut = vi.fn();
    const mockPatch = vi.fn();
    const mockDelete = vi.fn();

    return {
      data: initialData,
      setData: mockSetData,
      post: mockPost,
      get: mockGet,
      put: mockPut,
      patch: mockPatch,
      delete: mockDelete,
      processing: false,
      errors: {},
      hasErrors: false,
      progress: null,
      wasSuccessful: false,
      recentlySuccessful: false,
      transform: vi.fn(),
      defaults: vi.fn(),
      reset: vi.fn(),
      clearErrors: vi.fn(),
      setError: vi.fn(),
      cancel: vi.fn(),
    };
  }
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { customRender as render };
export { default as userEvent } from '@testing-library/user-event';
