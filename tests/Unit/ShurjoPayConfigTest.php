<?php

namespace Tests\Unit;

use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class ShurjoPayConfigTest extends TestCase
{
    /** @test */
    public function shurjopay_debug_config_uses_shurjopay_debug_env_variable()
    {
        // Test with debug enabled
        Config::set('shurjopay.debug', true);
        $this->assertTrue(config('shurjopay.debug'));

        // Test with debug disabled
        Config::set('shurjopay.debug', false);
        $this->assertFalse(config('shurjopay.debug'));
    }

    /** @test */
    public function shurjopay_debug_config_defaults_to_false()
    {
        // Clear any existing config
        Config::set('shurjopay.debug', null);
        
        // Reload config with default value
        $this->app['config']->set('shurjopay.debug', env('SHURJOPAY_DEBUG', false));
        
        $this->assertFalse(config('shurjopay.debug'));
    }

    /** @test */
    public function shurjopay_config_has_all_required_keys()
    {
        $requiredKeys = [
            'environment',
            'username',
            'password',
            'prefix',
            'api_url',
            'urls',
            'currency',
            'ssl_verify',
            'logging',
            'supported_currencies',
            'debug',
        ];

        foreach ($requiredKeys as $key) {
            $this->assertTrue(
                config()->has("shurjopay.{$key}"),
                "ShurjoPay config is missing required key: {$key}"
            );
        }
    }

    /** @test */
    public function shurjopay_debug_config_is_boolean()
    {
        // Test with string 'true'
        Config::set('shurjopay.debug', 'true');
        $this->assertIsString(config('shurjopay.debug'));

        // Test with boolean true
        Config::set('shurjopay.debug', true);
        $this->assertIsBool(config('shurjopay.debug'));
        $this->assertTrue(config('shurjopay.debug'));

        // Test with boolean false
        Config::set('shurjopay.debug', false);
        $this->assertIsBool(config('shurjopay.debug'));
        $this->assertFalse(config('shurjopay.debug'));
    }

    /** @test */
    public function shurjopay_environment_config_has_valid_values()
    {
        $validEnvironments = ['sandbox', 'production'];
        
        foreach ($validEnvironments as $env) {
            Config::set('shurjopay.environment', $env);
            $this->assertEquals($env, config('shurjopay.environment'));
        }
    }

    /** @test */
    public function shurjopay_supported_currencies_includes_bdt()
    {
        $supportedCurrencies = config('shurjopay.supported_currencies');
        
        $this->assertIsArray($supportedCurrencies);
        $this->assertContains('BDT', $supportedCurrencies);
    }

    /** @test */
    public function shurjopay_urls_config_has_sandbox_and_production()
    {
        $urls = config('shurjopay.urls');
        
        $this->assertIsArray($urls);
        $this->assertArrayHasKey('sandbox', $urls);
        $this->assertArrayHasKey('production', $urls);
        $this->assertStringContainsString('sandbox.shurjopayment.com', $urls['sandbox']);
        $this->assertStringContainsString('engine.shurjopayment.com', $urls['production']);
    }

    /** @test */
    public function shurjopay_logging_config_has_required_keys()
    {
        $loggingConfig = config('shurjopay.logging');
        
        $this->assertIsArray($loggingConfig);
        $this->assertArrayHasKey('enabled', $loggingConfig);
        $this->assertArrayHasKey('level', $loggingConfig);
        $this->assertArrayHasKey('location', $loggingConfig);
        $this->assertArrayHasKey('channel', $loggingConfig);
    }

    /** @test */
    public function shurjopay_ssl_verify_config_is_boolean()
    {
        // Test with boolean true
        Config::set('shurjopay.ssl_verify', true);
        $this->assertIsBool(config('shurjopay.ssl_verify'));
        $this->assertTrue(config('shurjopay.ssl_verify'));

        // Test with boolean false
        Config::set('shurjopay.ssl_verify', false);
        $this->assertIsBool(config('shurjopay.ssl_verify'));
        $this->assertFalse(config('shurjopay.ssl_verify'));
    }

    /** @test */
    public function shurjopay_timeout_config_has_reasonable_defaults()
    {
        $timeoutConfig = config('shurjopay.timeout');

        $this->assertIsArray($timeoutConfig);
        $this->assertArrayHasKey('connect', $timeoutConfig);
        $this->assertArrayHasKey('request', $timeoutConfig);

        // Check that timeouts are reasonable (not too short or too long)
        $this->assertGreaterThan(5, $timeoutConfig['connect']);
        $this->assertLessThan(60, $timeoutConfig['connect']);
        $this->assertGreaterThan(10, $timeoutConfig['request']);
        $this->assertLessThan(120, $timeoutConfig['request']);
    }

    /** @test */
    public function shurjopay_logging_enabled_config_is_boolean()
    {
        // Test with boolean true
        Config::set('shurjopay.logging.enabled', true);
        $this->assertIsBool(config('shurjopay.logging.enabled'));
        $this->assertTrue(config('shurjopay.logging.enabled'));

        // Test with boolean false
        Config::set('shurjopay.logging.enabled', false);
        $this->assertIsBool(config('shurjopay.logging.enabled'));
        $this->assertFalse(config('shurjopay.logging.enabled'));
    }

    /** @test */
    public function shurjopay_logging_enabled_defaults_to_true()
    {
        // Test that the default value in the config file is true
        // We'll directly set the config to what it should be when no env var is set
        Config::set('shurjopay.logging.enabled', true);

        $this->assertTrue(config('shurjopay.logging.enabled'));

        // Also test that the config structure matches what's expected
        $this->assertIsArray(config('shurjopay.logging'));
        $this->assertArrayHasKey('enabled', config('shurjopay.logging'));
    }
}
