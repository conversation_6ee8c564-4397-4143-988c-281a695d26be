import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '../utils/test-utils';
import { router } from '@inertiajs/react';
import PartDetails from '@/pages/search/part-details';
import { SharedData } from '@/types';

// Mock Inertia
vi.mock('@inertiajs/react', async () => {
    const actual = await vi.importActual('@inertiajs/react');
    return {
        ...actual,
        router: {
            post: vi.fn(),
        },
        usePage: vi.fn(() => ({
            props: {
                auth: { user: null }, // Guest user
                ziggy: { location: 'http://localhost' }
            } as SharedData,
            url: '/parts/test-part',
        })),
        Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
        Link: ({ href, children, ...props }: any) => (
            <a href={href} {...props}>
                {children}
            </a>
        ),
    };
});

// Mock route function
global.route = vi.fn((name: string, params?: any) => {
    const routes: Record<string, string> = {
        'register': '/register',
        'home': '/',
        'search.index': '/search',
    };
    return routes[name] || `/${name}`;
});

const mockPart = {
    id: 1,
    name: 'iPhone 13 Display',
    slug: 'iphone-13-display',
    part_number: 'IP13-DISP-001',
    manufacturer: 'Apple',
    description: 'High-quality replacement display for iPhone 13',
    specifications: {
        'Screen Size': '6.1 inches',
        'Resolution': '2532 x 1170',
        'Technology': 'Super Retina XDR OLED',
    },
    images: ['/images/iphone-13-display-1.jpg'],
    category: {
        id: 1,
        name: 'Displays',
        description: 'Mobile device displays and screens',
    },
    models: [
        {
            id: 1,
            name: 'iPhone 13',
            model_number: 'A2482',
            release_year: 2021,
            brand: { id: 1, name: 'Apple' },
            pivot: { compatibility_notes: 'Direct replacement', is_verified: true },
            is_blurred: false,
        },
        {
            id: 2,
            name: 'iPhone 13 Mini',
            model_number: 'A2481',
            release_year: 2021,
            brand: { id: 1, name: 'Apple' },
            pivot: { compatibility_notes: 'Compatible with modifications', is_verified: false },
            is_blurred: false,
        },
        {
            id: 3,
            name: 'iPhone 13 Pro',
            model_number: 'A2483',
            release_year: 2021,
            brand: { id: 1, name: 'Apple' },
            pivot: { compatibility_notes: 'Requires adapter', is_verified: true },
            is_blurred: true, // This model should be blurred
        },
        {
            id: 4,
            name: 'iPhone 13 Pro Max',
            model_number: 'A2484',
            release_year: 2021,
            brand: { id: 1, name: 'Apple' },
            pivot: { compatibility_notes: 'Not recommended', is_verified: false },
            is_blurred: true, // This model should be blurred
        },
    ],
};

const mockGuestSearchConfig = {
    enable_partial_results: true,
    max_visible_results: 2,
    blur_intensity: 'medium',
    show_signup_cta: true,
};

describe('Parts Compatibility Blur Effect', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Guest User Experience', () => {
        it('renders part details with blur effect for guest users', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            expect(screen.getByText('iPhone 13 Display')).toBeInTheDocument();
            expect(screen.getByText('Compatible Models (4)')).toBeInTheDocument();
        });

        it('shows sign-up CTA overlay when blur effect is enabled', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            expect(screen.getByText('Sign up to see all 4 compatible models')).toBeInTheDocument();
            expect(screen.getByText('View All Models')).toBeInTheDocument();
        });

        it('applies blur class to blurred models in table view', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            // Check that table view is default
            const tableRows = screen.getAllByRole('row');
            expect(tableRows.length).toBeGreaterThan(0);

            // The blur effect is applied via CSS classes, which we can't directly test
            // but we can verify the component renders without errors
            expect(screen.getByText('iPhone 13')).toBeInTheDocument();
            expect(screen.getByText('iPhone 13 Mini')).toBeInTheDocument();
        });

        it('applies blur class to blurred models in list view', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            // Switch to list view
            const listViewButton = screen.getByTitle('List View');
            fireEvent.click(listViewButton);

            // Verify list view elements are present
            expect(screen.getByText('Apple iPhone 13')).toBeInTheDocument();
            expect(screen.getByText('Apple iPhone 13 Mini')).toBeInTheDocument();
        });

        it('shows correct sign-up link in overlay', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            const signUpLink = screen.getByText('View All Models').closest('a');
            expect(signUpLink).toHaveAttribute('href', '/register');
        });

        it('does not show blur overlay when show_signup_cta is false', () => {
            const configWithoutCta = {
                ...mockGuestSearchConfig,
                show_signup_cta: false,
            };

            render(<PartDetails part={mockPart} guestSearchConfig={configWithoutCta} />);

            expect(screen.queryByText('Sign up to see all 4 compatible models')).not.toBeInTheDocument();
            expect(screen.queryByText('View All Models')).not.toBeInTheDocument();
        });

        it('does not show blur overlay when partial results are disabled', () => {
            const configDisabled = {
                ...mockGuestSearchConfig,
                enable_partial_results: false,
            };

            render(<PartDetails part={mockPart} guestSearchConfig={configDisabled} />);

            expect(screen.queryByText('Sign up to see all 4 compatible models')).not.toBeInTheDocument();
            expect(screen.queryByText('View All Models')).not.toBeInTheDocument();
        });

        it('handles parts with no blurred models correctly', () => {
            const partWithoutBlur = {
                ...mockPart,
                models: mockPart.models.map(model => ({ ...model, is_blurred: false })),
            };

            render(<PartDetails part={partWithoutBlur} guestSearchConfig={mockGuestSearchConfig} />);

            expect(screen.queryByText('Sign up to see all 4 compatible models')).not.toBeInTheDocument();
            expect(screen.queryByText('View All Models')).not.toBeInTheDocument();
        });

        it('handles parts with no models correctly', () => {
            const partWithoutModels = {
                ...mockPart,
                models: [],
            };

            render(<PartDetails part={partWithoutModels} guestSearchConfig={mockGuestSearchConfig} />);

            expect(screen.queryByText('Compatible Models')).not.toBeInTheDocument();
            expect(screen.queryByText('Sign up to see all')).not.toBeInTheDocument();
        });
    });

    describe('View Mode Toggle', () => {
        it('switches between table and list views correctly', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            // Default should be table view
            expect(screen.getAllByRole('row').length).toBeGreaterThan(0);

            // Switch to list view
            const listViewButton = screen.getByTitle('List View');
            fireEvent.click(listViewButton);

            // Should now show list view format
            expect(screen.getByText('Apple iPhone 13')).toBeInTheDocument();

            // Switch back to table view
            const tableViewButton = screen.getByTitle('Table View');
            fireEvent.click(tableViewButton);

            // Should show table view again
            expect(screen.getAllByRole('row').length).toBeGreaterThan(0);
        });

        it('maintains blur overlay in both view modes', () => {
            render(<PartDetails part={mockPart} guestSearchConfig={mockGuestSearchConfig} />);

            // Check overlay in table view
            expect(screen.getByText('Sign up to see all 4 compatible models')).toBeInTheDocument();

            // Switch to list view
            const listViewButton = screen.getByTitle('List View');
            fireEvent.click(listViewButton);

            // Overlay should still be present
            expect(screen.getByText('Sign up to see all 4 compatible models')).toBeInTheDocument();
        });
    });

    describe('Authenticated User Experience', () => {
        it('does not show blur effect for authenticated users', async () => {
            // Mock authenticated user
            const { usePage } = await import('@inertiajs/react');
            vi.mocked(usePage).mockReturnValue({
                props: {
                    auth: { user: { id: 1, name: 'Test User' } },
                    ziggy: { location: 'http://localhost' }
                } as SharedData,
                url: '/parts/test-part',
            });

            render(<PartDetails part={mockPart} />);

            // Should not show blur overlay for authenticated users
            expect(screen.queryByText('Sign up to see all 4 compatible models')).not.toBeInTheDocument();
            expect(screen.queryByText('View All Models')).not.toBeInTheDocument();
        });
    });
});
