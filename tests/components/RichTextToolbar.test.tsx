import { render, screen, fireEvent, userEvent } from '../utils/test-utils';
import { describe, it, expect, vi } from 'vitest';
import { RichTextToolbar } from '@/components/RichTextToolbar';

// Mock the LinkDialog component
vi.mock('@/components/LinkDialog', () => ({
    LinkDialog: ({ open, onConfirm, onOpenChange }: any) => (
        open ? (
            <div data-testid="link-dialog">
                <button onClick={() => onConfirm('https://example.com', 'Example')}>
                    Confirm Link
                </button>
                <button onClick={() => onOpenChange(false)}>Cancel</button>
            </div>
        ) : null
    ),
}));

describe('RichTextToolbar', () => {
    const mockEditor = {
        isActive: vi.fn((format, options) => {
            if (format === 'bold') return false;
            if (format === 'italic') return false;
            if (format === 'underline') return false;
            if (format === 'strike') return false;
            if (format === 'bulletList') return false;
            if (format === 'orderedList') return false;
            if (format === 'blockquote') return false;
            if (format === 'codeBlock') return false;
            if (format === 'link') return false;
            if (format === 'heading' && options?.level) return false;
            if (format === 'textAlign') return false;
            return false;
        }),
        chain: vi.fn(() => ({
            focus: vi.fn(() => ({
                toggleBold: vi.fn(() => ({ run: vi.fn() })),
                toggleItalic: vi.fn(() => ({ run: vi.fn() })),
                toggleUnderline: vi.fn(() => ({ run: vi.fn() })),
                toggleStrike: vi.fn(() => ({ run: vi.fn() })),
                setTextAlign: vi.fn(() => ({ run: vi.fn() })),
                toggleBulletList: vi.fn(() => ({ run: vi.fn() })),
                toggleOrderedList: vi.fn(() => ({ run: vi.fn() })),
                toggleBlockquote: vi.fn(() => ({ run: vi.fn() })),
                toggleCodeBlock: vi.fn(() => ({ run: vi.fn() })),
                setLink: vi.fn(() => ({ run: vi.fn() })),
                unsetLink: vi.fn(() => ({ run: vi.fn() })),
                undo: vi.fn(() => ({ run: vi.fn() })),
                redo: vi.fn(() => ({ run: vi.fn() })),
                clearNodes: vi.fn(() => ({ unsetAllMarks: vi.fn(() => ({ run: vi.fn() })) })),
                setParagraph: vi.fn(() => ({ run: vi.fn() })),
                toggleHeading: vi.fn(() => ({ run: vi.fn() })),
                insertContent: vi.fn(() => ({ run: vi.fn() })),
            })),
        })),
        can: vi.fn(() => ({
            undo: vi.fn(() => true),
            redo: vi.fn(() => true),
        })),
        getAttributes: vi.fn(() => ({ href: '' })),
        state: {
            selection: { from: 0, to: 5 },
            doc: { textBetween: vi.fn(() => 'selected text') },
        },
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders all formatting buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /italic/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /underline/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /strikethrough/i })).toBeInTheDocument();
    });

    it('renders heading selector', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('renders alignment controls', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('radio', { name: /align left/i })).toBeInTheDocument();
        expect(screen.getByRole('radio', { name: /align center/i })).toBeInTheDocument();
        expect(screen.getByRole('radio', { name: /align right/i })).toBeInTheDocument();
        expect(screen.getByRole('radio', { name: /justify/i })).toBeInTheDocument();
    });

    it('renders list buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /bullet list/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /ordered list/i })).toBeInTheDocument();
    });

    it('renders block element buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /blockquote/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /code block/i })).toBeInTheDocument();
    });

    it('renders link buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /add link/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /remove link/i })).toBeInTheDocument();
    });

    it('renders action buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /undo/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /redo/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /clear formatting/i })).toBeInTheDocument();
    });

    it('calls editor commands when buttons are clicked', async () => {
        const user = userEvent.setup();
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        const boldButton = screen.getByRole('button', { name: /bold/i });
        await user.click(boldButton);
        
        expect(mockEditor.chain).toHaveBeenCalled();
    });

    it('handles heading selection', async () => {
        const user = userEvent.setup();
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        const headingSelector = screen.getByRole('combobox');
        await user.click(headingSelector);
        
        // The heading selector should be interactive
        expect(headingSelector).toBeInTheDocument();
    });

    it('handles text alignment changes', async () => {
        const user = userEvent.setup();
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        const centerAlign = screen.getByRole('radio', { name: /align center/i });
        await user.click(centerAlign);
        
        expect(mockEditor.chain).toHaveBeenCalled();
    });

    it('opens link dialog when add link is clicked', async () => {
        const user = userEvent.setup();
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        const addLinkButton = screen.getByRole('button', { name: /add link/i });
        await user.click(addLinkButton);
        
        expect(screen.getByTestId('link-dialog')).toBeInTheDocument();
    });

    it('handles link confirmation from dialog', async () => {
        const user = userEvent.setup();
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        const addLinkButton = screen.getByRole('button', { name: /add link/i });
        await user.click(addLinkButton);
        
        const confirmButton = screen.getByText('Confirm Link');
        await user.click(confirmButton);
        
        expect(mockEditor.chain).toHaveBeenCalled();
    });

    it('disables buttons when disabled prop is true', () => {
        render(<RichTextToolbar editor={mockEditor as any} disabled={true} />);
        
        const boldButton = screen.getByRole('button', { name: /bold/i });
        expect(boldButton).toBeDisabled();
        
        const italicButton = screen.getByRole('button', { name: /italic/i });
        expect(italicButton).toBeDisabled();
    });

    it('shows active state for pressed buttons', () => {
        const activeEditor = {
            ...mockEditor,
            isActive: vi.fn((format) => format === 'bold'),
        };
        
        render(<RichTextToolbar editor={activeEditor as any} />);
        
        const boldButton = screen.getByRole('button', { name: /bold/i });
        expect(boldButton).toHaveAttribute('data-state', 'on');
    });

    it('disables undo/redo when not available', () => {
        const limitedEditor = {
            ...mockEditor,
            can: vi.fn(() => ({
                undo: vi.fn(() => false),
                redo: vi.fn(() => false),
            })),
        };
        
        render(<RichTextToolbar editor={limitedEditor as any} />);
        
        const undoButton = screen.getByRole('button', { name: /undo/i });
        const redoButton = screen.getByRole('button', { name: /redo/i });
        
        expect(undoButton).toBeDisabled();
        expect(redoButton).toBeDisabled();
    });

    it('disables remove link when no link is active', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        const removeLinkButton = screen.getByRole('button', { name: /remove link/i });
        expect(removeLinkButton).toBeDisabled();
    });

    it('enables remove link when link is active', () => {
        const linkEditor = {
            ...mockEditor,
            isActive: vi.fn((format) => format === 'link'),
        };
        
        render(<RichTextToolbar editor={linkEditor as any} />);
        
        const removeLinkButton = screen.getByRole('button', { name: /remove link/i });
        expect(removeLinkButton).not.toBeDisabled();
    });

    it('returns null when editor is not provided', () => {
        const { container } = render(<RichTextToolbar editor={null as any} />);

        // The MockAnalyticsProvider wrapper will still be there, but the RichTextToolbar content should be empty
        const analyticsWrapper = container.firstChild as HTMLElement;
        expect(analyticsWrapper).toHaveAttribute('data-testid', 'mock-analytics-provider');
        expect(analyticsWrapper.children.length).toBe(0);
    });
});

describe('RichTextToolbar Accessibility', () => {
    const mockEditor = {
        isActive: vi.fn(() => false),
        chain: vi.fn(() => ({
            focus: vi.fn(() => ({
                toggleBold: vi.fn(() => ({ run: vi.fn() })),
                toggleItalic: vi.fn(() => ({ run: vi.fn() })),
                toggleUnderline: vi.fn(() => ({ run: vi.fn() })),
                toggleStrike: vi.fn(() => ({ run: vi.fn() })),
                setTextAlign: vi.fn(() => ({ run: vi.fn() })),
                toggleBulletList: vi.fn(() => ({ run: vi.fn() })),
                toggleOrderedList: vi.fn(() => ({ run: vi.fn() })),
                toggleBlockquote: vi.fn(() => ({ run: vi.fn() })),
                toggleCodeBlock: vi.fn(() => ({ run: vi.fn() })),
                setLink: vi.fn(() => ({ run: vi.fn() })),
                unsetLink: vi.fn(() => ({ run: vi.fn() })),
                undo: vi.fn(() => ({ run: vi.fn() })),
                redo: vi.fn(() => ({ run: vi.fn() })),
                clearNodes: vi.fn(() => ({ unsetAllMarks: vi.fn(() => ({ run: vi.fn() })) })),
                setParagraph: vi.fn(() => ({ run: vi.fn() })),
                toggleHeading: vi.fn(() => ({ run: vi.fn() })),
            })),
        })),
        can: vi.fn(() => ({
            undo: vi.fn(() => true),
            redo: vi.fn(() => true),
        })),
        getAttributes: vi.fn(() => ({ href: '' })),
        state: {
            selection: { from: 0, to: 0 },
            doc: { textBetween: vi.fn(() => '') },
        },
    };

    it('has proper ARIA labels for all buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /bold/i })).toHaveAttribute('aria-label', 'Bold');
        expect(screen.getByRole('button', { name: /italic/i })).toHaveAttribute('aria-label', 'Italic');
        expect(screen.getByRole('button', { name: /underline/i })).toHaveAttribute('aria-label', 'Underline');
        expect(screen.getByRole('button', { name: /strikethrough/i })).toHaveAttribute('aria-label', 'Strikethrough');
    });

    it('has proper ARIA labels for alignment controls', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('radio', { name: /align left/i })).toHaveAttribute('aria-label', 'Align left');
        expect(screen.getByRole('radio', { name: /align center/i })).toHaveAttribute('aria-label', 'Align center');
        expect(screen.getByRole('radio', { name: /align right/i })).toHaveAttribute('aria-label', 'Align right');
        expect(screen.getByRole('radio', { name: /justify/i })).toHaveAttribute('aria-label', 'Justify');
    });

    it('has proper ARIA labels for list buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /bullet list/i })).toHaveAttribute('aria-label', 'Bullet list');
        expect(screen.getByRole('button', { name: /ordered list/i })).toHaveAttribute('aria-label', 'Ordered list');
    });

    it('has proper ARIA labels for action buttons', () => {
        render(<RichTextToolbar editor={mockEditor as any} />);
        
        expect(screen.getByRole('button', { name: /undo/i })).toHaveAttribute('aria-label', 'Undo');
        expect(screen.getByRole('button', { name: /redo/i })).toHaveAttribute('aria-label', 'Redo');
        expect(screen.getByRole('button', { name: /clear formatting/i })).toHaveAttribute('aria-label', 'Clear formatting');
    });
});
