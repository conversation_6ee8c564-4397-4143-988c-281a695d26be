import { render, screen } from '../utils/test-utils';
import { describe, it, expect } from 'vitest';
import { Users } from 'lucide-react';
import { StatCard, statCardColors } from '@/components/StatCard';
import { cn } from '@/lib/utils';

describe('StatCard', () => {
    const defaultProps = {
        title: 'Total Users',
        value: 150,
        icon: Users,
        gradient: statCardColors.primary.gradient,
        iconBg: statCardColors.primary.iconBg,
    };

    it('renders correctly with basic props', () => {
        render(<StatCard {...defaultProps} />);
        
        expect(screen.getByText('Total Users')).toBeInTheDocument();
        expect(screen.getByText('150')).toBeInTheDocument();
    });

    it('renders with string value', () => {
        render(<StatCard {...defaultProps} value="1.2K" />);
        
        expect(screen.getByText('1.2K')).toBeInTheDocument();
    });

    it('applies custom className', () => {
        const { container } = render(
            <StatCard {...defaultProps} className="custom-class" />
        );

        // The StatCard is wrapped by MockAnalyticsProvider, so we need to find the actual StatCard div
        const statCardElement = container.querySelector('div[class*="relative"]');

        expect(statCardElement).toHaveClass('custom-class');
    });

    it('applies gradient background', () => {
        const { container } = render(<StatCard {...defaultProps} />);

        const statCardElement = container.querySelector('div[class*="relative"]');
        expect(statCardElement).toHaveClass(statCardColors.primary.gradient);
    });

    it('renders icon correctly', () => {
        const { container } = render(<StatCard {...defaultProps} />);

        // Check if the icon is rendered (Users icon should be present)
        const iconElement = container.querySelector('svg');
        expect(iconElement).toBeInTheDocument();
    });

    it('has proper responsive classes', () => {
        const { container } = render(<StatCard {...defaultProps} />);

        const statCardElement = container.querySelector('div[class*="relative"]');
        expect(statCardElement).toHaveClass('p-4', 'sm:p-6');
        expect(statCardElement).toHaveClass('min-h-[120px]', 'sm:min-h-[140px]');
    });

    it('has hover effects', () => {
        const { container } = render(<StatCard {...defaultProps} />);

        const statCardElement = container.querySelector('div[class*="relative"]');
        expect(statCardElement).toHaveClass('hover:shadow-xl', 'hover:scale-[1.02]');
    });

    it('renders background patterns', () => {
        const { container } = render(<StatCard {...defaultProps} />);
        
        // Check for background pattern elements
        const backgroundElements = container.querySelectorAll('.absolute');
        expect(backgroundElements.length).toBeGreaterThan(0);
    });

    describe('Color Schemes', () => {
        it('applies primary color scheme', () => {
            const { container } = render(
                <StatCard
                    {...defaultProps}
                    gradient={statCardColors.primary.gradient}
                    iconBg={statCardColors.primary.iconBg}
                />
            );

            const statCardElement = container.querySelector('div[class*="relative"]');
            expect(statCardElement).toHaveClass(statCardColors.primary.gradient);
        });

        it('applies success color scheme', () => {
            const { container } = render(
                <StatCard
                    {...defaultProps}
                    gradient={statCardColors.success.gradient}
                    iconBg={statCardColors.success.iconBg}
                />
            );

            const statCardElement = container.querySelector('div[class*="relative"]');
            expect(statCardElement).toHaveClass(statCardColors.success.gradient);
        });

        it('applies warning color scheme', () => {
            const { container } = render(
                <StatCard
                    {...defaultProps}
                    gradient={statCardColors.warning.gradient}
                    iconBg={statCardColors.warning.iconBg}
                />
            );

            const statCardElement = container.querySelector('div[class*="relative"]');
            expect(statCardElement).toHaveClass(statCardColors.warning.gradient);
        });

        it('applies danger color scheme', () => {
            const { container } = render(
                <StatCard
                    {...defaultProps}
                    gradient={statCardColors.danger.gradient}
                    iconBg={statCardColors.danger.iconBg}
                />
            );

            const statCardElement = container.querySelector('div[class*="relative"]');
            expect(statCardElement).toHaveClass(statCardColors.danger.gradient);
        });
    });

    describe('Accessibility', () => {
        it('has proper text contrast with white text', () => {
            render(<StatCard {...defaultProps} />);
            
            const title = screen.getByText('Total Users');
            const value = screen.getByText('150');
            
            expect(title).toHaveClass('text-white/90');
            expect(value).toHaveClass('text-white');
        });

        it('has proper semantic structure', () => {
            render(<StatCard {...defaultProps} />);

            // Title should be accessible
            const title = screen.getByText('Total Users');
            expect(title).toBeInTheDocument();
        });
    });

    describe('Responsive Design', () => {
        it('has responsive padding', () => {
            const { container } = render(<StatCard {...defaultProps} />);

            const statCardElement = container.querySelector('div[class*="relative"]');
            expect(statCardElement).toHaveClass('p-4', 'sm:p-6');
        });

        it('has responsive icon sizes', () => {
            const { container } = render(<StatCard {...defaultProps} />);
            
            const iconElement = container.querySelector('svg');
            expect(iconElement).toHaveClass('h-6', 'w-6', 'sm:h-8', 'sm:w-8');
        });

        it('has responsive text sizes', () => {
            render(<StatCard {...defaultProps} />);
            
            const title = screen.getByText('Total Users');
            const value = screen.getByText('150');
            
            expect(title).toHaveClass('text-xs', 'sm:text-sm');
            expect(value).toHaveClass('text-2xl', 'sm:text-3xl');
        });
    });
});
