import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../utils/test-utils';
import { router } from '@inertiajs/react';
import GuestResults from '@/pages/search/guest-results';
import PartDetails from '@/pages/search/part-details';

// Mock Inertia router
vi.mock('@inertiajs/react', async () => {
    const actual = await vi.importActual('@inertiajs/react');
    return {
        ...actual,
        router: {
            visit: vi.fn(),
            get: vi.fn(),
            post: vi.fn(),
        },
        usePage: vi.fn(() => ({
            props: {
                auth: { user: null }, // Guest user
                ziggy: { location: 'http://localhost' }
            }
        })),
        Link: ({ href, children, ...props }: any) => (
            <a href={href} {...props}>{children}</a>
        ),
        Head: ({ children }: any) => <>{children}</>,
    };
});

// Mock the global route helper function
global.route = vi.fn((name: string, params?: any) => {
    const routes: Record<string, string> = {
        'home': '/',
        'parts.show': params ? `/parts/${params}` : '/parts',
        'register': '/register',
        'search.index': '/search',
    };
    return routes[name] || `/${name}`;
});

// Mock components
vi.mock('@/components/Watermark', () => ({
    AutoWatermark: () => <div data-testid="watermark">Watermark</div>
}));

vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: any) => <div data-testid="app-layout">{children}</div>
}));

vi.mock('@/components/security/CompatibleModelsProtection', () => ({
    default: ({ children }: any) => <div data-testid="protection">{children}</div>
}));

describe('Guest Search Flow', () => {
    const mockPart = {
        id: 1,
        name: 'iPhone 13 Display',
        slug: 'iphone-13-display',
        part_number: 'IP13-DISP-001',
        description: 'High-quality replacement display for iPhone 13',
        specifications: null,
        images: null,
        category: {
            id: 1,
            name: 'Display',
            description: 'Display components'
        },
        models: [
            {
                id: 1,
                name: 'iPhone 13',
                model_number: 'A2482',
                release_year: 2021,
                brand: {
                    id: 1,
                    name: 'Apple'
                },
                pivot: {
                    compatibility_notes: 'Perfect fit',
                    is_verified: true
                }
            }
        ]
    };

    const mockGuestResults = {
        results: {
            data: [mockPart],
            total: 1,
            per_page: 10,
            current_page: 1,
            last_page: 1
        },
        query: 'iPhone',
        guest_search_used: true,
        message: 'Free search used',
        signup_url: '/register'
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Guest Results Component', () => {
        it('renders search results with View Details button', () => {
            render(<GuestResults {...mockGuestResults} />);

            expect(screen.getByText('iPhone 13 Display')).toBeInTheDocument();
            expect(screen.getByText('View Details')).toBeInTheDocument();
            expect(screen.getByText('Search Again')).toBeInTheDocument();
        });

        it('View Details button links to correct part page', () => {
            render(<GuestResults {...mockGuestResults} />);

            const viewDetailsButton = screen.getByRole('link', { name: /view details/i });
            expect(viewDetailsButton).toHaveAttribute('href', '/parts/iphone-13-display');
        });

        it('Search Again button links to home page', () => {
            render(<GuestResults {...mockGuestResults} />);

            const searchAgainButton = screen.getByRole('link', { name: /search again/i });
            expect(searchAgainButton).toHaveAttribute('href', '/');
        });

        it('Back to Home button is present', () => {
            render(<GuestResults {...mockGuestResults} />);

            const backToHomeButton = screen.getByRole('link', { name: /back to home/i });
            expect(backToHomeButton).toHaveAttribute('href', '/');
        });

        it('displays part information correctly', () => {
            render(<GuestResults {...mockGuestResults} />);

            expect(screen.getByText('iPhone 13 Display')).toBeInTheDocument();
            expect(screen.getByText(/IP13-DISP-001/)).toBeInTheDocument();
            expect(screen.getByText('High-quality replacement display for iPhone 13')).toBeInTheDocument();
            expect(screen.getAllByText('Display')[0]).toBeInTheDocument();
            expect(screen.getByText('Apple iPhone 13')).toBeInTheDocument();
        });
    });

    describe('Part Details Component', () => {
        it('renders part details for guest users', () => {
            render(<PartDetails part={mockPart} />);

            expect(screen.getByText('iPhone 13 Display')).toBeInTheDocument();
            expect(screen.getByText('Back to Home')).toBeInTheDocument();
            expect(screen.getByText('Search Again')).toBeInTheDocument();
        });

        it('shows Sign up to Save button for guest users', () => {
            render(<PartDetails part={mockPart} />);

            expect(screen.getByText('Sign up to Save')).toBeInTheDocument();
            expect(screen.queryByText('Save')).not.toBeInTheDocument();
        });

        it('navigation buttons work correctly for guests', () => {
            render(<PartDetails part={mockPart} />);

            const backToHomeButton = screen.getByRole('link', { name: /back to home/i });
            expect(backToHomeButton).toHaveAttribute('href', '/');

            const searchAgainButton = screen.getByRole('link', { name: /search again/i });
            expect(searchAgainButton).toHaveAttribute('href', '/');
        });

        it('Sign up to Save button links to register page', () => {
            render(<PartDetails part={mockPart} />);

            const signupButton = screen.getByRole('link', { name: /sign up to save/i });
            expect(signupButton).toHaveAttribute('href', '/register');
        });

        it('displays part specifications and compatibility info', () => {
            render(<PartDetails part={mockPart} />);

            expect(screen.getAllByText('Display')[0]).toBeInTheDocument();
            expect(screen.getAllByText(/IP13-DISP-001/)[0]).toBeInTheDocument();
            expect(screen.getByText('Apple')).toBeInTheDocument();
            expect(screen.getByText('iPhone 13')).toBeInTheDocument();
        });

        it('share functionality works', async () => {
            // Mock navigator.clipboard
            Object.assign(navigator, {
                clipboard: {
                    writeText: vi.fn(),
                },
            });

            render(<PartDetails part={mockPart} />);

            const shareButton = screen.getByRole('button', { name: /share part/i });
            fireEvent.click(shareButton);

            // Should attempt to copy URL to clipboard
            await waitFor(() => {
                expect(navigator.clipboard.writeText).toHaveBeenCalled();
            });
        });
    });

    describe('Responsive Design', () => {
        it('renders properly on mobile screens', () => {
            // Mock mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375,
            });

            render(<GuestResults {...mockGuestResults} />);

            expect(screen.getByText('iPhone 13 Display')).toBeInTheDocument();
            expect(screen.getByText('View Details')).toBeInTheDocument();
        });

        it('navigation buttons stack properly on mobile', () => {
            render(<PartDetails part={mockPart} />);

            const navigationContainer = screen.getByText('Back to Home').closest('div');
            expect(navigationContainer).toHaveClass('flex-col');
        });
    });
});
