import { renderHook, act } from '../utils/test-utils';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useAppearance, initializeTheme, type Appearance } from '@/hooks/use-appearance';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock window.matchMedia
const matchMediaMock = vi.fn();

// Mock console.log to avoid noise in tests
const consoleLogMock = vi.fn();

// Spy objects for DOM methods
let documentElementClassListSpy: any;
let documentElementSetAttributeSpy: any;
let documentBodyStyleSpy: any;
let documentBodyRemoveAttributeSpy: any;

describe('useAppearance Hook', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    // Setup matchMedia mock
    Object.defineProperty(window, 'matchMedia', {
      value: matchMediaMock,
      writable: true,
    });

    // Setup console.log mock
    Object.defineProperty(console, 'log', {
      value: consoleLogMock,
      writable: true,
    });

    // Spy on document methods instead of replacing document
    documentElementClassListSpy = {
      add: vi.spyOn(document.documentElement.classList, 'add'),
      remove: vi.spyOn(document.documentElement.classList, 'remove'),
      contains: vi.spyOn(document.documentElement.classList, 'contains'),
    };

    documentElementSetAttributeSpy = vi.spyOn(document.documentElement, 'setAttribute');

    documentBodyStyleSpy = {
      removeProperty: vi.spyOn(document.body.style, 'removeProperty'),
    };

    documentBodyRemoveAttributeSpy = vi.spyOn(document.body, 'removeAttribute');

    // Default matchMedia implementation
    matchMediaMock.mockReturnValue({
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    // Reset document cookie
    document.cookie = '';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Hook Initialization', () => {
    it('should initialize with system appearance when no saved preference', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const { result } = renderHook(() => useAppearance());
      
      expect(result.current.appearance).toBe('system');
      expect(localStorageMock.getItem).toHaveBeenCalledWith('appearance');
    });

    it('should initialize with saved appearance from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('dark');
      
      const { result } = renderHook(() => useAppearance());
      
      expect(result.current.appearance).toBe('dark');
    });

    it('should apply theme on mount', () => {
      localStorageMock.getItem.mockReturnValue('light');
      
      renderHook(() => useAppearance());
      
      expect(documentElementClassListSpy.remove).toHaveBeenCalledWith('light', 'dark');
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('light');
      expect(documentElementSetAttributeSpy).toHaveBeenCalledWith('data-theme', 'light');
    });
  });

  describe('setAppearance Function', () => {
    it('should update appearance state and apply theme', () => {
      localStorageMock.getItem.mockReturnValue('system');
      
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('dark');
      });
      
      expect(result.current.appearance).toBe('dark');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('appearance', 'dark');
      expect(document.cookie).toContain('appearance=dark');
    });

    it('should handle light mode correctly', () => {
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('light');
      });
      
      expect(documentElementClassListSpy.remove).toHaveBeenCalledWith('light', 'dark');
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('light');
      expect(documentElementSetAttributeSpy).toHaveBeenCalledWith('data-theme', 'light');
    });

    it('should handle dark mode correctly', () => {
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('dark');
      });
      
      expect(documentElementClassListSpy.remove).toHaveBeenCalledWith('light', 'dark');
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('dark');
      expect(documentElementSetAttributeSpy).toHaveBeenCalledWith('data-theme', 'dark');
    });

    it('should handle system mode with light preference', () => {
      matchMediaMock.mockReturnValue({
        matches: false, // Light mode
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      });
      
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('system');
      });
      
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('light');
      expect(documentElementSetAttributeSpy).toHaveBeenCalledWith('data-theme', 'system');
    });

    it('should handle system mode with dark preference', () => {
      matchMediaMock.mockReturnValue({
        matches: true, // Dark mode
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      });
      
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('system');
      });
      
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('dark');
      expect(documentElementSetAttributeSpy).toHaveBeenCalledWith('data-theme', 'system');
    });
  });

  describe('System Theme Change Handling', () => {
    it('should listen for system theme changes when appearance is system', () => {
      const mockMediaQuery = {
        matches: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
      
      matchMediaMock.mockReturnValue(mockMediaQuery);
      
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('system');
      });
      
      expect(mockMediaQuery.addEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });

    it('should clean up event listeners on unmount', () => {
      const mockMediaQuery = {
        matches: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
      
      matchMediaMock.mockReturnValue(mockMediaQuery);
      
      const { unmount } = renderHook(() => useAppearance());
      
      unmount();
      
      expect(mockMediaQuery.removeEventListener).toHaveBeenCalled();
    });
  });

  describe('Cookie Handling', () => {
    it('should set cookie with correct format and expiration', () => {
      const { result } = renderHook(() => useAppearance());
      
      act(() => {
        result.current.setAppearance('dark');
      });
      
      // Check that the cookie was set with the correct value
      expect(document.cookie).toContain('appearance=dark');
      // Note: jsdom may not parse cookie attributes the same way as real browsers
      // so we just check that the main value is set correctly
    });
  });

  describe('initializeTheme Function', () => {
    it('should initialize theme from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('dark');
      
      initializeTheme();
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('appearance');
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('dark');
    });

    it('should default to system when no saved preference', () => {
      localStorageMock.getItem.mockReturnValue(null);
      matchMediaMock.mockReturnValue({
        matches: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      });
      
      initializeTheme();
      
      expect(documentElementClassListSpy.add).toHaveBeenCalledWith('light');
    });

    it('should set up system theme change listener', () => {
      const mockMediaQuery = {
        matches: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
      
      matchMediaMock.mockReturnValue(mockMediaQuery);
      
      initializeTheme();
      
      expect(mockMediaQuery.addEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });
  });

  describe('Edge Cases', () => {
    it('should work when localStorage returns null', () => {
      // Test normal case where localStorage is available but returns null
      localStorageMock.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useAppearance());
      expect(result.current.appearance).toBe('system');
    });

    it('should work with valid matchMedia implementation', () => {
      // Test that the hook works with a proper matchMedia mock
      matchMediaMock.mockReturnValue({
        matches: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      });

      const { result } = renderHook(() => useAppearance());

      act(() => {
        result.current.setAppearance('system');
      });

      expect(result.current.appearance).toBe('system');
    });
  });
});
