import React from 'react';
import { render, screen, waitFor } from '../utils/test-utils';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../mocks/server';
import { http, HttpResponse } from 'msw';
import DynamicFooter from '@/components/DynamicFooter';
import DynamicNavbar from '@/components/DynamicNavbar';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    Link: ({ children, href, ...props }: any) => (
        <a href={href} {...props}>
            {children}
        </a>
    ),
    usePage: () => ({
        props: {
            auth: {
                user: {
                    name: 'Test User',
                    email: '<EMAIL>',
                },
            },
        },
        url: '/test',
    }),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
    Menu: () => <div data-testid="menu-icon">☰</div>,
    Search: () => <div data-testid="search-icon">🔍</div>,
    LayoutGrid: () => <div data-testid="layout-grid-icon">⊞</div>,
}));

// Mock useAdmin hook
vi.mock('@/hooks/use-admin', () => ({
    useAdmin: vi.fn(() => false),
}));

// Mock AppLogo component
vi.mock('@/components/app-logo', () => ({
    default: () => <div data-testid="app-logo">Logo</div>,
}));

// Mock AppLogoIcon component
vi.mock('@/components/app-logo-icon', () => ({
    default: () => <div data-testid="app-logo-icon">P</div>,
}));

// Mock UI components
vi.mock('@/components/ui/button', () => ({
    Button: ({ children, ...props }: any) => (
        <button {...props}>{children}</button>
    ),
}));

vi.mock('@/components/ui/input', () => ({
    Input: ({ ...props }: any) => <input {...props} />,
}));

vi.mock('@/components/ui/navigation-menu', () => ({
    NavigationMenu: ({ children }: any) => <nav>{children}</nav>,
    NavigationMenuItem: ({ children }: any) => <div>{children}</div>,
    NavigationMenuList: ({ children }: any) => <ul>{children}</ul>,
    NavigationMenuTrigger: ({ children }: any) => <button>{children}</button>,
    NavigationMenuContent: ({ children }: any) => <div>{children}</div>,
    NavigationMenuLink: ({ children, asChild, ...props }: any) =>
        asChild ? <div {...props}>{children}</div> : <a {...props}>{children}</a>,
}));

vi.mock('@/components/ui/sheet', () => ({
    Sheet: ({ children }: any) => <div>{children}</div>,
    SheetContent: ({ children }: any) => <div>{children}</div>,
    SheetHeader: ({ children }: any) => <div>{children}</div>,
    SheetTitle: ({ children }: any) => <h2>{children}</h2>,
    SheetTrigger: ({ children }: any) => <div>{children}</div>,
}));

describe('DynamicFooter', () => {
    beforeEach(() => {
        // Reset MSW handlers before each test
        server.resetHandlers();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('renders footer with default configuration', async () => {
        // The default MSW handler already provides the correct response
        render(<DynamicFooter />);

        await waitFor(() => {
            expect(screen.getByText('Test footer content')).toBeInTheDocument();
        });

        expect(screen.getByText('© 2024 Test Company')).toBeInTheDocument();
        expect(screen.getByText('Privacy')).toBeInTheDocument();
        expect(screen.getByText('Terms')).toBeInTheDocument();
        expect(screen.getByTestId('app-logo-icon')).toBeInTheDocument();
    });

    it('does not render when footer is disabled', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/footer-config', () => {
                return HttpResponse.json({
                    footer_enabled: false,
                });
            })
        );

        const { container } = render(<DynamicFooter />);

        await waitFor(() => {
            // The MockAnalyticsProvider wrapper will still be there, but the DynamicFooter content should be empty
            const analyticsWrapper = container.firstChild as HTMLElement;
            expect(analyticsWrapper).toHaveAttribute('data-testid', 'mock-analytics-provider');
            expect(analyticsWrapper.children.length).toBe(0);
        });
    });

    it('renders fallback footer on API error', async () => {
        // Override the default handler to return an error
        server.use(
            http.get('/api/footer-config', () => {
                return HttpResponse.json({ error: 'API Error' }, { status: 500 });
            })
        );

        render(<DynamicFooter />);

        await waitFor(() => {
            expect(screen.getByText('FixHaat')).toBeInTheDocument();
        });

        expect(screen.getByText('The comprehensive mobile parts database for professionals')).toBeInTheDocument();
    });

    it('renders columns layout correctly', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/footer-config', () => {
                return HttpResponse.json({
                    footer_enabled: true,
                    footer_layout: 'columns',
                    footer_background_color: '#1f2937',
                    footer_text_color: '#ffffff',
                    footer_content: 'Test content',
                    footer_copyright: '© 2024 Test',
                    footer_links: [{ title: 'Link 1', url: '/link1', target: '_self' }],
                    footer_social_links: [],
                    footer_show_logo: true,
                    footer_logo_position: 'left',
                    footer_menu_ids: [],
                    footer_menus: [],
                    footer_newsletter_enabled: false,
                });
            })
        );

        render(<DynamicFooter />);

        await waitFor(() => {
            expect(screen.getByText('Test content')).toBeInTheDocument();
        });

        expect(screen.getByText('Link 1')).toBeInTheDocument();
    });

    it('applies custom styling from configuration', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/footer-config', () => {
                return HttpResponse.json({
                    footer_enabled: true,
                    footer_layout: 'simple',
                    footer_background_color: '#ff0000',
                    footer_text_color: '#00ff00',
                    footer_content: 'Styled content',
                    footer_copyright: '© 2024 Styled',
                    footer_links: [],
                    footer_social_links: [],
                    footer_show_logo: false,
                    footer_logo_position: 'center',
                    footer_menu_ids: [],
                    footer_menus: [],
                    footer_newsletter_enabled: false,
                });
            })
        );

        render(<DynamicFooter />);

        await waitFor(() => {
            const footer = screen.getByRole('contentinfo');
            expect(footer).toHaveStyle({
                backgroundColor: '#ff0000',
                color: '#00ff00',
            });
        });
    });

    it('renders newsletter section when enabled', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/footer-config', () => {
                return HttpResponse.json({
                    footer_enabled: true,
                    footer_layout: 'simple',
                    footer_background_color: '#1f2937',
                    footer_text_color: '#ffffff',
                    footer_content: 'Test content',
                    footer_copyright: '© 2024 Test',
                    footer_links: [],
                    footer_social_links: [],
                    footer_show_logo: true,
                    footer_logo_position: 'center',
                    footer_menu_ids: [],
                    footer_menus: [],
                    footer_newsletter_enabled: true,
                    footer_newsletter_title: 'Subscribe Now',
                    footer_newsletter_description: 'Get weekly updates',
                    footer_newsletter_placeholder: 'Enter your email',
                });
            })
        );

        render(<DynamicFooter />);

        await waitFor(() => {
            expect(screen.getByText('Subscribe Now')).toBeInTheDocument();
        });

        expect(screen.getByText('Get weekly updates')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument();
        expect(screen.getByText('SUBSCRIBE')).toBeInTheDocument();
    });

    it('renders menu columns when menus are provided', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/footer-config', () => {
                return HttpResponse.json({
                    footer_enabled: true,
                    footer_layout: 'simple',
                    footer_background_color: '#1f2937',
                    footer_text_color: '#ffffff',
                    footer_content: 'Test content',
                    footer_copyright: '© 2024 Test',
                    footer_links: [],
                    footer_social_links: [],
                    footer_show_logo: true,
                    footer_logo_position: 'center',
                    footer_menu_ids: [1],
                    footer_menus: [
                        {
                            id: 1,
                            name: 'Quick Links',
                            location: 'footer',
                            root_items: [
                                {
                                    id: 1,
                                    title: 'About Us',
                                    url: '/about',
                                    computed_url: '/about',
                                    target: '_self',
                                },
                                {
                                    id: 2,
                                    title: 'Contact',
                                    url: '/contact',
                                    computed_url: '/contact',
                                    target: '_self',
                                },
                            ],
                        },
                    ],
                    footer_newsletter_enabled: false,
                    footer_newsletter_title: 'Newsletter',
                    footer_newsletter_description: 'Subscribe to our newsletter',
                    footer_newsletter_placeholder: 'Your email',
                });
            })
        );

        render(<DynamicFooter />);

        await waitFor(() => {
            expect(screen.getByText('Quick Links')).toBeInTheDocument();
        });

        expect(screen.getByText('About Us')).toBeInTheDocument();
        expect(screen.getByText('Contact')).toBeInTheDocument();
    });
});

describe('DynamicNavbar', () => {
    beforeEach(() => {
        // Reset MSW handlers before each test
        server.resetHandlers();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('renders navbar with default configuration', async () => {
        // The default MSW handler already provides the correct response
        render(<DynamicNavbar />);

        await waitFor(() => {
            expect(screen.getByTestId('app-logo')).toBeInTheDocument();
        });

        // For authenticated users, search icon should be hidden and Dashboard button should be shown
        expect(screen.queryByTestId('search-icon')).not.toBeInTheDocument();
        expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    it('does not render when navbar is disabled', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: false,
                });
            })
        );

        const { container } = render(<DynamicNavbar />);

        await waitFor(() => {
            // The MockAnalyticsProvider wrapper will still be there, but the DynamicNavbar content should be empty
            const analyticsWrapper = container.firstChild as HTMLElement;
            expect(analyticsWrapper).toHaveAttribute('data-testid', 'mock-analytics-provider');
            expect(analyticsWrapper.children.length).toBe(0);
        });
    });

    it('renders menu items when provided', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'Home',
                            url: '/',
                            target: '_self',
                            children: [],
                        },
                        {
                            id: 2,
                            title: 'About',
                            url: '/about',
                            target: '_self',
                            children: [
                                {
                                    id: 3,
                                    title: 'Team',
                                    url: '/about/team',
                                    target: '_self',
                                    children: [],
                                },
                            ],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            const homeElements = screen.getAllByText('Home');
            expect(homeElements.length).toBeGreaterThan(0);
        });

        const aboutElements = screen.getAllByText('About');
        expect(aboutElements.length).toBeGreaterThan(0);
    });

    it('hides search button when configured', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: null,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: false,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [],
                });
            })
        );

        render(<DynamicNavbar showSearch={false} />);

        await waitFor(() => {
            expect(screen.getByTestId('app-logo')).toBeInTheDocument();
        });

        expect(screen.queryByTestId('search-icon')).not.toBeInTheDocument();
    });

    it('applies custom styling from configuration', async () => {
        // Override the default handler for this test
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: null,
                    navbar_background_color: '#ff0000',
                    navbar_text_color: '#00ff00',
                    navbar_logo_position: 'center',
                    navbar_show_search: true,
                    navbar_sticky: false,
                    navbar_style: 'minimal',
                    menu_items: [],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            const navbar = screen.getByRole('navigation');
            expect(navbar).toHaveStyle({
                backgroundColor: '#ff0000',
                color: '#00ff00',
            });
        });
    });
});
