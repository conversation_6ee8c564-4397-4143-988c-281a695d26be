import { render, screen, fireEvent, act } from '../../utils/test-utils';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import React from 'react';

// Test component that uses the hook
function TestComponent({
    onHookReady,
    options
}: {
    onHookReady?: (hook: any) => void;
    options?: any;
}) {
    const deleteConfirmation = useDeleteConfirmation();

    React.useEffect(() => {
        onHookReady?.(deleteConfirmation);
    }, [deleteConfirmation, onHookReady]);

    const defaultOptions = {
        title: 'Delete Item',
        onConfirm: vi.fn(),
    };

    return (
        <div data-testid="test-component">
            <button
                data-testid="show-confirmation"
                onClick={() => deleteConfirmation.showDeleteConfirmation(options || defaultOptions)}
            >
                Show Confirmation
            </button>
            <button
                data-testid="confirm-delete"
                onClick={() => deleteConfirmation.confirmDelete(options || defaultOptions)}
            >
                Confirm Delete
            </button>
        </div>
    );
}

describe('useDeleteConfirmation Hook', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // Ensure clean DOM state
        document.body.innerHTML = '';
    });

    afterEach(() => {
        // Clean up any modal containers that might have been added
        const modals = document.querySelectorAll('[data-testid="delete-confirmation-modal"]');
        modals.forEach(modal => modal.remove());

        // Clean up body
        document.body.innerHTML = '';
    });

    it('returns showDeleteConfirmation and confirmDelete methods', () => {
        let hookResult: any;

        render(<TestComponent onHookReady={(hook) => { hookResult = hook; }} />);

        expect(hookResult).toHaveProperty('showDeleteConfirmation');
        expect(hookResult).toHaveProperty('confirmDelete');
        expect(typeof hookResult.showDeleteConfirmation).toBe('function');
        expect(typeof hookResult.confirmDelete).toBe('function');
    });

    it('confirmDelete is an alias for showDeleteConfirmation', () => {
        let hookResult: any;

        render(<TestComponent onHookReady={(hook) => { hookResult = hook; }} />);

        expect(hookResult.confirmDelete).toBe(hookResult.showDeleteConfirmation);
    });

    it('creates modal container when showDeleteConfirmation is called', () => {
        render(<TestComponent />);

        const showButton = screen.getByTestId('show-confirmation');

        act(() => {
            fireEvent.click(showButton);
        });

        // Check that a modal container was created in the DOM
        // The modal uses Tailwind classes, not inline styles
        const modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).toBeInTheDocument();
    });

    it('creates modal container when confirmDelete is called', () => {
        render(<TestComponent />);

        const confirmButton = screen.getByTestId('confirm-delete');

        act(() => {
            fireEvent.click(confirmButton);
        });

        // Check that a modal container was created in the DOM
        // The modal uses Tailwind classes, not inline styles
        const modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).toBeInTheDocument();
    });

    it('passes correct props to modal component', () => {
        const mockOptions = {
            title: 'Delete Item',
            description: 'Are you sure you want to delete this item?',
            onConfirm: vi.fn(),
            onCancel: vi.fn(),
            confirmText: 'Delete',
            cancelText: 'Cancel',
        };

        render(<TestComponent options={mockOptions} />);

        const showButton = screen.getByTestId('show-confirmation');

        act(() => {
            fireEvent.click(showButton);
        });

        // Check that the modal was created and contains the expected content
        // The modal uses Tailwind classes, not inline styles
        const modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).toBeInTheDocument();

        // The modal should contain the title text
        expect(modalContainer).toHaveTextContent('Delete Item');
    });

    it('uses default text when confirmText and cancelText are not provided', () => {
        const mockOptions = {
            title: 'Delete Item',
            onConfirm: vi.fn(),
        };

        render(<TestComponent options={mockOptions} />);

        const showButton = screen.getByTestId('show-confirmation');

        act(() => {
            fireEvent.click(showButton);
        });

        // Check that the modal was created with the title
        // The modal uses Tailwind classes, not inline styles
        const modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).toBeInTheDocument();
        expect(modalContainer).toHaveTextContent('Delete Item');
    });

    it('handles multiple confirmation dialogs', () => {
        let hookResult: any;

        render(<TestComponent onHookReady={(hook) => { hookResult = hook; }} />);

        const mockOptions1 = {
            title: 'Delete Item 1',
            onConfirm: vi.fn(),
        };

        const mockOptions2 = {
            title: 'Delete Item 2',
            onConfirm: vi.fn(),
        };

        act(() => {
            hookResult.showDeleteConfirmation(mockOptions1);
            hookResult.showDeleteConfirmation(mockOptions2);
        });

        // Check that multiple modal containers exist
        // The modal uses Tailwind classes, not inline styles
        const modalContainers = document.querySelectorAll('.fixed.inset-0');
        expect(modalContainers.length).toBeGreaterThanOrEqual(1);
    });

    it('provides onClose callback that cleans up modal', () => {
        render(<TestComponent />);

        const showButton = screen.getByTestId('show-confirmation');

        act(() => {
            fireEvent.click(showButton);
        });

        // Check that the modal was created
        // The modal uses Tailwind classes, not inline styles
        let modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).toBeInTheDocument();

        // Simulate closing the modal by removing it from DOM
        // (In real usage, the modal component would handle this)
        act(() => {
            modalContainer?.remove();
        });

        // Verify modal is no longer in DOM
        modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).not.toBeInTheDocument();
    });

    it('handles missing optional callbacks gracefully', () => {
        const mockOptions = {
            title: 'Delete Item',
            onConfirm: vi.fn(),
            // onCancel is optional and not provided
        };

        render(<TestComponent options={mockOptions} />);

        const showButton = screen.getByTestId('show-confirmation');

        expect(() => {
            act(() => {
                fireEvent.click(showButton);
            });
        }).not.toThrow();

        // Check that the modal was created successfully
        // The modal uses Tailwind classes, not inline styles
        const modalContainer = document.querySelector('.fixed.inset-0');
        expect(modalContainer).toBeInTheDocument();
        expect(modalContainer).toHaveTextContent('Delete Item');
    });
});
