import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import GoogleAnalyticsManager from '@/utils/analytics';
import type { AnalyticsConfig } from '@/types/analytics';

// Mock gtag and dataLayer
const mockGtag = vi.fn();
const mockDataLayer: any[] = [];

Object.defineProperty(window, 'gtag', {
  value: mockGtag,
  writable: true,
});

Object.defineProperty(window, 'dataLayer', {
  value: mockDataLayer,
  writable: true,
});

// Mock document.createElement for script loading
const mockScript = {
  async: false,
  src: '',
  onload: null as (() => void) | null,
  onerror: null as (() => void) | null,
};

const mockAppendChild = vi.fn();

Object.defineProperty(document, 'createElement', {
  value: vi.fn(() => mockScript),
});

Object.defineProperty(document.head, 'appendChild', {
  value: mockAppendChild,
});

// Mock navigator.doNotTrack - will be overridden in specific tests
Object.defineProperty(navigator, 'doNotTrack', {
  value: '0',
  writable: true,
  configurable: true,
});

describe('GoogleAnalyticsManager', () => {
  let analytics: GoogleAnalyticsManager;
  let config: AnalyticsConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    mockDataLayer.length = 0;
    
    config = {
      enabled: true,
      measurement_id: 'G-TEST123456',
      debug: false,
      cookie_domain: 'auto',
      cookie_expires: 63072000,
      anonymize_ip: true,
      consent_mode: {
        enabled: true,
        default_settings: {
          ad_storage: 'denied',
          analytics_storage: 'denied',
          ad_user_data: 'denied',
          ad_personalization: 'denied',
          functionality_storage: 'granted',
          security_storage: 'granted',
        },
      },
      auto_track: {
        page_views: true,
        file_downloads: true,
        external_links: true,
        form_submissions: true,
        scroll_depth: true,
      },
      privacy: {
        cookie_consent_required: true,
        respect_do_not_track: true,
        data_retention_days: 730,
      },
    };

    analytics = new GoogleAnalyticsManager(config);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should not initialize when disabled', async () => {
      config.enabled = false;
      analytics = new GoogleAnalyticsManager(config);
      
      await analytics.initialize();
      
      expect(analytics.isInitialized()).toBe(false);
      expect(mockAppendChild).not.toHaveBeenCalled();
    });

    it('should not initialize without measurement ID', async () => {
      config.measurement_id = undefined;
      analytics = new GoogleAnalyticsManager(config);
      
      await analytics.initialize();
      
      expect(analytics.isInitialized()).toBe(false);
      expect(mockAppendChild).not.toHaveBeenCalled();
    });

    it('should respect Do Not Track', async () => {
      // Create a new analytics instance with DNT enabled
      const dntConfig = { ...config };
      const dntAnalytics = new GoogleAnalyticsManager(dntConfig);

      // Override the doNotTrack property for this test
      Object.defineProperty(navigator, 'doNotTrack', {
        value: '1',
        writable: true,
        configurable: true,
      });

      await dntAnalytics.initialize();

      expect(dntAnalytics.isInitialized()).toBe(false);
      expect(mockAppendChild).not.toHaveBeenCalled();

      // Reset back to '0' for other tests
      Object.defineProperty(navigator, 'doNotTrack', {
        value: '0',
        writable: true,
        configurable: true,
      });
    });

    it('should initialize successfully with valid config', async () => {
      // Mock successful script loading
      mockAppendChild.mockImplementation((script) => {
        // Simulate script loading success synchronously
        if (script.onload) {
          script.onload();
        }
      });

      await analytics.initialize();

      expect(analytics.isInitialized()).toBe(true);
      expect(mockAppendChild).toHaveBeenCalled();
      expect(mockDataLayer).toContainEqual(['consent', 'default', config.consent_mode?.default_settings]);
      expect(mockDataLayer).toContainEqual(['js', expect.any(Date)]);
      expect(mockDataLayer).toContainEqual(['config', 'G-TEST123456', expect.any(Object)]);
    });

    it('should handle script loading errors gracefully', async () => {
      mockAppendChild.mockImplementation((script) => {
        // Simulate script loading error
        setTimeout(() => {
          if (script.onerror) {
            script.onerror();
          }
        }, 0);
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await analytics.initialize();

      expect(analytics.isInitialized()).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Failed to initialize Google Analytics:', expect.any(Error));

      consoleSpy.mockRestore();
    });
  });

  describe('tracking methods', () => {
    beforeEach(async () => {
      // Initialize analytics for tracking tests
      mockAppendChild.mockImplementation((script) => {
        // Simulate script loading success synchronously
        if (script.onload) {
          script.onload();
        }
      });

      await analytics.initialize();

      // Grant consent for tracking
      analytics.updateConsent({
        analytics_storage: 'granted',
        ad_storage: 'granted',
      });

      // Clear mocks after setup so we can track subsequent calls
      vi.clearAllMocks();
    });

    it('should track page views', () => {
      analytics.trackPageView({
        page_title: 'Test Page',
        page_location: 'https://example.com/test',
      });

      expect(mockDataLayer).toContainEqual(['event', 'page_view', {
        page_title: 'Test Page',
        page_location: 'https://example.com/test',
        page_referrer: '',
      }]);
    });

    it('should track custom events', () => {
      analytics.trackEvent({
        action: 'click',
        category: 'button',
        label: 'header-cta',
        value: 1,
      });

      expect(mockDataLayer).toContainEqual(['event', 'click', {
        event_category: 'button',
        event_label: 'header-cta',
        value: 1,
      }]);
    });

    it('should track custom events with custom names', () => {
      analytics.trackCustomEvent({
        event_name: 'user_signup',
        parameters: {
          method: 'email',
          user_type: 'premium',
        },
      });

      expect(mockDataLayer).toContainEqual(['event', 'user_signup', {
        method: 'email',
        user_type: 'premium',
      }]);
    });

    it('should track ecommerce events', () => {
      analytics.trackEcommerce({
        transaction_id: 'T12345',
        value: 99.99,
        currency: 'USD',
        items: [{
          item_id: 'SKU123',
          item_name: 'Test Product',
          price: 99.99,
          quantity: 1,
        }],
      });

      expect(mockDataLayer).toContainEqual(['event', 'purchase', {
        transaction_id: 'T12345',
        value: 99.99,
        currency: 'USD',
        items: [{
          item_id: 'SKU123',
          item_name: 'Test Product',
          price: 99.99,
          quantity: 1,
        }],
      }]);
    });

    it('should set user properties', () => {
      analytics.setUserProperties({
        user_id: '12345',
        user_role: 'admin',
        subscription_plan: 'premium',
      });

      expect(mockDataLayer).toContainEqual(['config', 'G-TEST123456', {
        user_id: '12345',
      }]);

      expect(mockDataLayer).toContainEqual(['event', 'user_properties', {
        user_role: 'admin',
        subscription_plan: 'premium',
      }]);
    });

    it('should not track when consent is not given', () => {
      // Revoke consent
      analytics.updateConsent({
        analytics_storage: 'denied',
      });

      analytics.trackPageView();

      expect(mockDataLayer).toContainEqual(['consent', 'update', {
        analytics_storage: 'denied',
      }]);

      // Should not track page view after consent revoked
      expect(mockDataLayer).not.toContainEqual(['event', 'page_view', expect.any(Object)]);
    });
  });

  describe('consent management', () => {
    beforeEach(async () => {
      mockAppendChild.mockImplementation((script) => {
        // Simulate script loading success synchronously
        if (script.onload) {
          script.onload();
        }
      });

      await analytics.initialize();
      vi.clearAllMocks();
    });

    it('should update consent settings', () => {
      const consentSettings = {
        analytics_storage: 'granted' as const,
        ad_storage: 'denied' as const,
      };

      analytics.updateConsent(consentSettings);

      expect(mockDataLayer).toContainEqual(['consent', 'update', consentSettings]);
    });

    it('should not update consent when consent mode is disabled', () => {
      config.consent_mode = { enabled: false, default_settings: {} as any };
      analytics = new GoogleAnalyticsManager(config);

      analytics.updateConsent({
        analytics_storage: 'granted',
      });

      expect(mockDataLayer).not.toContainEqual(['consent', 'update', expect.any(Object)]);
    });
  });

  describe('debug mode', () => {
    beforeEach(async () => {
      // Initialize analytics for debug mode tests
      mockAppendChild.mockImplementation((script) => {
        if (script.onload) {
          script.onload();
        }
      });
      await analytics.initialize();
      vi.clearAllMocks();
    });

    it('should enable debug mode', () => {
      expect(analytics.isInitialized()).toBe(true); // Verify analytics is initialized
      analytics.enableDebugMode();

      expect(mockDataLayer).toContainEqual(['config', 'G-TEST123456', {
        debug_mode: true,
      }]);
    });

    it('should disable debug mode', () => {
      expect(analytics.isInitialized()).toBe(true); // Verify analytics is initialized
      analytics.disableDebugMode();

      expect(mockDataLayer).toContainEqual(['config', 'G-TEST123456', {
        debug_mode: false,
      }]);
    });

    it('should log debug messages when debug mode is enabled', () => {
      config.debug = true;
      analytics = new GoogleAnalyticsManager(config);
      
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      analytics.trackEvent({
        action: 'test',
        category: 'debug',
      });
      
      expect(consoleSpy).toHaveBeenCalledWith('[Analytics] Analytics not initialized', '');
      
      consoleSpy.mockRestore();
    });
  });
});
