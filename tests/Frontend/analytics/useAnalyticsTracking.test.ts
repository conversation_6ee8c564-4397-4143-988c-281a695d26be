import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  renderHook,
  mockTrackEvent,
  mockTrackCustomEvent,
  mockTrackEcommerce
} from '../../utils/test-utils';
import {
  useInteractionTracking,
  useSearchTracking,
  useEcommerceTracking,
  useEngagementTracking,
  useErrorTracking,
} from '@/hooks/use-analytics-tracking';

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-page',
    href: 'https://example.com/test-page',
  },
  writable: true,
});

describe('Analytics Tracking Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useInteractionTracking', () => {
    it('should track button clicks', () => {
      const { result } = renderHook(() => useInteractionTracking());
      
      result.current.trackButtonClick('submit-form', '/checkout');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'click',
        category: 'button',
        label: 'submit-form',
        custom_parameters: {
          location: '/checkout',
        },
      });
    });

    it('should track link clicks', () => {
      const { result } = renderHook(() => useInteractionTracking());
      
      result.current.trackLinkClick('https://example.com/external', 'External Link');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'click',
        category: 'link',
        label: 'https://example.com/external',
        custom_parameters: {
          link_text: 'External Link',
          source_page: '/test-page',
        },
      });
    });

    it('should track form submissions', () => {
      const { result } = renderHook(() => useInteractionTracking());
      
      result.current.trackFormSubmission('contact-form', true);
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'submit_success',
        category: 'form',
        label: 'contact-form',
        custom_parameters: {
          form_location: '/test-page',
        },
      });
    });

    it('should track modal interactions', () => {
      const { result } = renderHook(() => useInteractionTracking());
      
      result.current.trackModalOpen('user-settings');
      result.current.trackModalClose('user-settings', 'escape');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'open',
        category: 'modal',
        label: 'user-settings',
        custom_parameters: {
          trigger_page: '/test-page',
        },
      });
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'close',
        category: 'modal',
        label: 'user-settings',
        custom_parameters: {
          close_method: 'escape',
        },
      });
    });
  });

  describe('useSearchTracking', () => {
    it('should track search queries', () => {
      const { result } = renderHook(() => useSearchTracking());
      
      result.current.trackSearch('mobile phone', 25, 'product');
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'search',
        parameters: {
          search_term: 'mobile phone',
          search_type: 'product',
          results_count: 25,
          page_location: '/test-page',
        },
      });
    });

    it('should track search result clicks', () => {
      const { result } = renderHook(() => useSearchTracking());
      
      result.current.trackSearchResultClick('mobile phone', 3, 'product-123');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'click',
        category: 'search_result',
        label: 'mobile phone',
        value: 3,
        custom_parameters: {
          result_id: 'product-123',
          result_position: 3,
        },
      });
    });

    it('should track search filters', () => {
      const { result } = renderHook(() => useSearchTracking());
      
      result.current.trackSearchFilter('brand', 'Apple', 'mobile phone');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'filter',
        category: 'search',
        label: 'brand:Apple',
        custom_parameters: {
          filter_type: 'brand',
          filter_value: 'Apple',
          search_query: 'mobile phone',
        },
      });
    });

    it('should track search sorting', () => {
      const { result } = renderHook(() => useSearchTracking());
      
      result.current.trackSearchSort('price', 'asc', 'mobile phone');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'sort',
        category: 'search',
        label: 'price_asc',
        custom_parameters: {
          sort_by: 'price',
          sort_order: 'asc',
          search_query: 'mobile phone',
        },
      });
    });
  });

  describe('useEcommerceTracking', () => {
    it('should track purchases', () => {
      const { result } = renderHook(() => useEcommerceTracking());
      
      const items = [
        { item_id: 'SKU123', item_name: 'Test Product', price: 99.99, quantity: 1 }
      ];
      
      result.current.trackPurchase('T12345', 99.99, 'USD', items);
      
      expect(mockTrackEcommerce).toHaveBeenCalledWith({
        transaction_id: 'T12345',
        value: 99.99,
        currency: 'USD',
        items,
      });
    });

    it('should track add to cart events', () => {
      const { result } = renderHook(() => useEcommerceTracking());
      
      result.current.trackAddToCart('SKU123', 'Test Product', 99.99, 2);
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'add_to_cart',
        parameters: {
          currency: 'USD',
          value: 199.98,
          items: [{
            item_id: 'SKU123',
            item_name: 'Test Product',
            price: 99.99,
            quantity: 2,
          }],
        },
      });
    });

    it('should track item views', () => {
      const { result } = renderHook(() => useEcommerceTracking());
      
      result.current.trackViewItem('SKU123', 'Test Product', 'Electronics', 99.99);
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'view_item',
        parameters: {
          currency: 'USD',
          value: 99.99,
          items: [{
            item_id: 'SKU123',
            item_name: 'Test Product',
            category: 'Electronics',
            price: 99.99,
          }],
        },
      });
    });

    it('should track checkout initiation', () => {
      const { result } = renderHook(() => useEcommerceTracking());
      
      const items = [
        { item_id: 'SKU123', item_name: 'Test Product', price: 99.99, quantity: 1 }
      ];
      
      result.current.trackBeginCheckout(99.99, 'USD', items);
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'begin_checkout',
        parameters: {
          currency: 'USD',
          value: 99.99,
          items,
        },
      });
    });
  });

  describe('useEngagementTracking', () => {
    it('should track time on page', () => {
      const { result } = renderHook(() => useEngagementTracking());
      
      result.current.trackTimeOnPage(120.5, 'home-page');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'time_on_page',
        category: 'engagement',
        label: 'home-page',
        value: 121, // Rounded
      });
    });

    it('should track file downloads', () => {
      const { result } = renderHook(() => useEngagementTracking());
      
      result.current.trackFileDownload('manual.pdf', 'pdf', 1024000);
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'file_download',
        parameters: {
          file_name: 'manual.pdf',
          file_type: 'pdf',
          file_size: 1024000,
          page_location: '/test-page',
        },
      });
    });

    it('should track video interactions', () => {
      const { result } = renderHook(() => useEngagementTracking());
      
      result.current.trackVideoPlay('Product Demo', 300);
      result.current.trackVideoComplete('Product Demo', 300);
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'video_play',
        parameters: {
          video_title: 'Product Demo',
          video_duration: 300,
          page_location: '/test-page',
        },
      });
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'video_complete',
        parameters: {
          video_title: 'Product Demo',
          video_duration: 300,
          page_location: '/test-page',
        },
      });
    });

    it('should track social sharing', () => {
      const { result } = renderHook(() => useEngagementTracking());
      
      result.current.trackSocialShare('twitter', 'article', 'article-123');
      
      expect(mockTrackEvent).toHaveBeenCalledWith({
        action: 'share',
        category: 'social',
        label: 'twitter',
        custom_parameters: {
          content_type: 'article',
          content_id: 'article-123',
          page_location: '/test-page',
        },
      });
    });
  });

  describe('useErrorTracking', () => {
    it('should track JavaScript errors', () => {
      const { result } = renderHook(() => useErrorTracking());
      
      result.current.trackError('TypeError: Cannot read property', 'javascript', true);
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'exception',
        parameters: {
          description: 'TypeError: Cannot read property',
          error_type: 'javascript',
          fatal: true,
          page_location: '/test-page',
        },
      });
    });

    it('should track API errors', () => {
      const { result } = renderHook(() => useErrorTracking());
      
      result.current.trackApiError('/api/users', 500, 'Internal Server Error');
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'api_error',
        parameters: {
          endpoint: '/api/users',
          status_code: 500,
          error_message: 'Internal Server Error',
          page_location: '/test-page',
        },
      });
    });

    it('should track performance metrics', () => {
      const { result } = renderHook(() => useErrorTracking());
      
      result.current.trackPerformance('page_load_time', 1250, 'ms');
      
      expect(mockTrackCustomEvent).toHaveBeenCalledWith({
        event_name: 'performance_metric',
        parameters: {
          metric_name: 'page_load_time',
          metric_value: 1250,
          metric_unit: 'ms',
          page_location: '/test-page',
        },
      });
    });
  });
});
