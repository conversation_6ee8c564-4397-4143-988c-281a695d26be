import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor, userEvent } from '../../utils/test-utils';
import CookieConsent from '@/components/analytics/CookieConsent';

// Mock the analytics utility
vi.mock('@/utils/analytics', () => ({
  getAnalytics: vi.fn(() => ({
    updateConsent: vi.fn(),
  })),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('CookieConsent', () => {
  const mockOnConsentChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render cookie consent banner when no consent is stored', () => {
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    expect(screen.getByText('We use cookies')).toBeInTheDocument();
    expect(screen.getByText(/We use cookies and similar technologies/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Accept All' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Reject All' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Customize/ })).toBeInTheDocument();
  });

  it('should not render banner when valid consent is stored', () => {
    const storedConsent = {
      preferences: {
        analytics: true,
        marketing: false,
        functional: true,
        necessary: true,
      },
      version: '1.0',
      timestamp: new Date().toISOString(),
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(storedConsent));
    
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    expect(screen.queryByText('We use cookies')).not.toBeInTheDocument();
  });

  it('should handle accept all button click', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    const acceptButton = screen.getByRole('button', { name: 'Accept All' });
    await user.click(acceptButton);
    
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'cookie_consent_preferences',
      expect.stringContaining('"analytics":true')
    );
    
    expect(mockOnConsentChange).toHaveBeenCalledWith({
      analytics_storage: 'granted',
      ad_storage: 'granted',
      ad_user_data: 'granted',
      ad_personalization: 'granted',
      functionality_storage: 'granted',
      security_storage: 'granted',
    });
  });

  it('should handle reject all button click', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    const rejectButton = screen.getByRole('button', { name: 'Reject All' });
    await user.click(rejectButton);
    
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'cookie_consent_preferences',
      expect.stringContaining('"analytics":false')
    );
    
    expect(mockOnConsentChange).toHaveBeenCalledWith({
      analytics_storage: 'denied',
      ad_storage: 'denied',
      ad_user_data: 'denied',
      ad_personalization: 'denied',
      functionality_storage: 'denied',
      security_storage: 'granted',
    });
  });

  it('should open customization dialog', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    const customizeButton = screen.getByRole('button', { name: /Customize/ });
    await user.click(customizeButton);
    
    await waitFor(() => {
      expect(screen.getByText('Cookie Preferences')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Necessary Cookies')).toBeInTheDocument();
    expect(screen.getByText('Functional Cookies')).toBeInTheDocument();
    expect(screen.getByText('Analytics Cookies')).toBeInTheDocument();
    expect(screen.getByText('Marketing Cookies')).toBeInTheDocument();
  });

  it('should allow customizing cookie preferences', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    // Open customization dialog
    const customizeButton = screen.getByRole('button', { name: /Customize/ });
    await user.click(customizeButton);
    
    await waitFor(() => {
      expect(screen.getByText('Cookie Preferences')).toBeInTheDocument();
    });
    
    // Toggle analytics cookies
    const analyticsSwitch = screen.getByLabelText('Analytics cookies');
    await user.click(analyticsSwitch);
    
    // Save preferences
    const saveButton = screen.getByRole('button', { name: 'Save Preferences' });
    await user.click(saveButton);
    
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'cookie_consent_preferences',
      expect.stringContaining('"analytics":true')
    );
    
    expect(mockOnConsentChange).toHaveBeenCalledWith({
      analytics_storage: 'granted',
      ad_storage: 'denied',
      ad_user_data: 'denied',
      ad_personalization: 'denied',
      functionality_storage: 'granted',
      security_storage: 'granted',
    });
  });

  it('should not allow disabling necessary cookies', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    // Open customization dialog
    const customizeButton = screen.getByRole('button', { name: /Customize/ });
    await user.click(customizeButton);
    
    await waitFor(() => {
      expect(screen.getByText('Cookie Preferences')).toBeInTheDocument();
    });
    
    // Necessary cookies switch should be disabled
    const necessarySwitch = screen.getByLabelText('Necessary cookies (always enabled)');
    expect(necessarySwitch).toBeDisabled();
    expect(necessarySwitch).toBeChecked();
  });

  it('should close banner when close button is clicked', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    const closeButton = screen.getByLabelText('Close cookie banner');
    await user.click(closeButton);
    
    expect(screen.queryByText('We use cookies')).not.toBeInTheDocument();
  });

  it('should handle invalid stored consent gracefully', () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-json');
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    // Should show banner when stored consent is invalid
    expect(screen.getByText('We use cookies')).toBeInTheDocument();
    expect(consoleSpy).toHaveBeenCalledWith('Error parsing stored consent:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  it('should show banner when consent version is outdated', () => {
    const outdatedConsent = {
      preferences: {
        analytics: true,
        marketing: false,
        functional: true,
        necessary: true,
      },
      version: '0.9', // Outdated version
      timestamp: new Date().toISOString(),
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(outdatedConsent));
    
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    // Should show banner when version is outdated
    expect(screen.getByText('We use cookies')).toBeInTheDocument();
  });

  it('should cancel customization dialog', async () => {
    const user = userEvent.setup();
    render(<CookieConsent onConsentChange={mockOnConsentChange} />);
    
    // Open customization dialog
    const customizeButton = screen.getByRole('button', { name: /Customize/ });
    await user.click(customizeButton);
    
    await waitFor(() => {
      expect(screen.getByText('Cookie Preferences')).toBeInTheDocument();
    });
    
    // Cancel dialog
    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    await user.click(cancelButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Cookie Preferences')).not.toBeInTheDocument();
    });
    
    // Should not save preferences
    expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    expect(mockOnConsentChange).not.toHaveBeenCalled();
  });
});
