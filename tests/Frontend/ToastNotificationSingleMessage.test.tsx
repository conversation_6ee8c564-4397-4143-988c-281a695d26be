import { render, screen, waitFor } from '../utils/test-utils';
import { router } from '@inertiajs/react';
import { toast } from 'sonner';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import FlashMessageHandler from '@/components/flash-message-handler';
import { usePage } from '@inertiajs/react';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
    router: {
        post: vi.fn(),
        reload: vi.fn(),
    },
    usePage: vi.fn(),
}));

vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
        warning: vi.fn(),
        info: vi.fn(),
    },
}));

describe('Toast Notification Single Message Fix', () => {
    const mockUsePage = usePage as any;
    const mockToast = toast as any;
    const mockRouter = router as any;

    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('FlashMessageHandler Single Toast Display', () => {
        it('should display only one success toast for brands import', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Skipped 10 duplicate brands successfully.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Skipped 10 duplicate brands successfully.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should display only one success toast for models import', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Imported 5 models successfully.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Imported 5 models successfully.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should display only one success toast for categories import', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Imported 3 categories successfully.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Imported 3 categories successfully.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should display only one success toast for parts import', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Imported 15 parts successfully.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Imported 15 parts successfully.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should not display duplicate toasts when flash message changes', async () => {
            // First render with one message
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'First message',
                    },
                },
            });

            const { rerender } = render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith('First message');
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });

            // Clear mocks and update with new message
            vi.clearAllMocks();
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Second message',
                    },
                },
            });

            rerender(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith('Second message');
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should handle import errors without showing duplicate success messages', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Import completed successfully.',
                        import_errors: ['Row 2: Invalid data', 'Row 5: Missing field'],
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                // Should only show the success message from flash
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Import completed successfully.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
                // Import errors should be handled separately by the component
            });
        });

        it('should handle multiple flash message types without duplication', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Operation completed successfully.',
                        warning: 'Some warnings occurred.',
                        info: 'Additional information.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith('Operation completed successfully.');
                expect(mockToast.warning).toHaveBeenCalledWith('Some warnings occurred.');
                expect(mockToast.info).toHaveBeenCalledWith('Additional information.');
                
                // Each should be called exactly once
                expect(mockToast.success).toHaveBeenCalledTimes(1);
                expect(mockToast.warning).toHaveBeenCalledTimes(1);
                expect(mockToast.info).toHaveBeenCalledTimes(1);
            });
        });

        it('should not show any toast when no flash messages are present', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {},
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).not.toHaveBeenCalled();
                expect(mockToast.error).not.toHaveBeenCalled();
                expect(mockToast.warning).not.toHaveBeenCalled();
                expect(mockToast.info).not.toHaveBeenCalled();
            });
        });

        it('should not show any toast when flash is undefined', async () => {
            mockUsePage.mockReturnValue({
                props: {},
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).not.toHaveBeenCalled();
                expect(mockToast.error).not.toHaveBeenCalled();
                expect(mockToast.warning).not.toHaveBeenCalled();
                expect(mockToast.info).not.toHaveBeenCalled();
            });
        });
    });
});
