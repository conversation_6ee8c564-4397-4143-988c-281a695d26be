import { render } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import AnalyticsProvider from '@/components/analytics/AnalyticsProvider';

// Mock Inertia's usePage hook
const mockUsePage = vi.fn();
vi.mock('@inertiajs/react', () => ({
  usePage: () => mockUsePage(),
}));

// Mock analytics utilities
vi.mock('@/utils/analytics', () => ({
  initializeAnalytics: vi.fn(() => ({
    initialize: vi.fn(),
    trackPageView: vi.fn(),
    trackEvent: vi.fn(),
    trackCustomEvent: vi.fn(),
    trackEcommerce: vi.fn(),
    setUserProperties: vi.fn(),
    updateConsent: vi.fn(),
  })),
  getAnalytics: vi.fn(),
}));

// Mock CookieConsent component
vi.mock('@/components/analytics/CookieConsent', () => ({
  default: ({ onConsentChange }: { onConsentChange: (consent: any) => void }) => (
    <div data-testid="cookie-consent">
      <button onClick={() => onConsentChange({ analytics_storage: 'granted' })}>
        Accept
      </button>
    </div>
  ),
}));

describe('AnalyticsProvider Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock usePage to return valid Inertia props
    mockUsePage.mockReturnValue({
      props: {
        analytics: {
          enabled: true,
          google_analytics: {
            measurement_id: 'GA_MEASUREMENT_ID',
          },
          privacy: {
            cookie_consent_required: false,
          },
        },
      },
    });
  });

  it('should render without errors when usePage is available', () => {
    expect(() => {
      render(
        <AnalyticsProvider>
          <div data-testid="test-child">Test Content</div>
        </AnalyticsProvider>
      );
    }).not.toThrow();
  });

  it('should handle missing analytics config gracefully', () => {
    mockUsePage.mockReturnValue({
      props: {},
    });

    expect(() => {
      render(
        <AnalyticsProvider>
          <div data-testid="test-child">Test Content</div>
        </AnalyticsProvider>
      );
    }).not.toThrow();
  });

  it('should handle disabled analytics config', () => {
    mockUsePage.mockReturnValue({
      props: {
        analytics: {
          enabled: false,
        },
      },
    });

    expect(() => {
      render(
        <AnalyticsProvider>
          <div data-testid="test-child">Test Content</div>
        </AnalyticsProvider>
      );
    }).not.toThrow();
  });

  it('should render children correctly', () => {
    const { getByTestId } = render(
      <AnalyticsProvider>
        <div data-testid="test-child">Test Content</div>
      </AnalyticsProvider>
    );

    expect(getByTestId('test-child')).toBeInTheDocument();
  });
});
