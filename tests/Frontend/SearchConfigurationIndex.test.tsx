import React from 'react';
import { render, screen, fireEvent, waitFor, within, userEvent } from '../utils/test-utils';
import { router, useForm } from '@inertiajs/react';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import SearchConfigurationIndex from '../../resources/js/pages/admin/SearchConfiguration/Index';

// Create mock functions that will be shared
const mockRouterPost = vi.fn();
const mockSetData = vi.fn();
const mockUseForm = vi.fn();

// Mock Inertia router with proper form state management
vi.mock('@inertiajs/react', () => {
    let mockFormData: any = {};

    return {
        router: {
            post: (...args: any[]) => {
                console.log('Router.post called with:', args);
                mockRouterPost(...args);
                return Promise.resolve();
            },
        },
        useForm: (initialData: any) => {
            // Initialize form data with the provided initial data
            mockFormData = { ...initialData };
            const formInstance = {
                data: mockFormData,
                setData: (key: string, value: any) => {
                    if (typeof key === 'string') {
                        mockFormData[key] = value;
                    } else {
                        // Handle object updates
                        mockFormData = { ...mockFormData, ...key };
                    }
                    mockSetData(key, value);
                },
                post: vi.fn(),
                processing: false,
                errors: {},
                reset: vi.fn(() => {
                    mockFormData = { ...initialData };
                }),
            };
            mockUseForm(formInstance);
            return formInstance;
        },
        Head: ({ title }: { title: string }) => null,
        Link: ({ href, children, ...props }: { href: string; children: React.ReactNode; [key: string]: any }) => (
            React.createElement('a', { href, ...props }, children)
        ),
        usePage: () => ({
            props: {
                auth: {
                    user: {
                        id: 1,
                        name: 'Test Admin',
                        email: '<EMAIL>'
                    }
                },
                flash: {}
            },
            url: '/admin/search-config'
        }),
    };
});

// Mock AppLayout
vi.mock('../../resources/js/layouts/app-layout', () => ({
    default: ({ children, breadcrumbs }: { children: React.ReactNode; breadcrumbs?: any[] }) => (
        <div data-testid="app-layout">
            {breadcrumbs && (
                <nav data-testid="breadcrumbs">
                    {breadcrumbs.map((crumb, index) => (
                        <span key={index} data-testid={`breadcrumb-${index}`}>
                            {crumb.title}
                        </span>
                    ))}
                </nav>
            )}
            {children}
        </div>
    ),
}));

// Mock MediaPicker component
vi.mock('../../resources/js/components/MediaPicker', () => ({
    default: ({ onSelect, value, ...props }: any) => (
        <div data-testid="media-picker" data-value={value} {...props}>
            <button onClick={() => onSelect && onSelect('test-image-url.jpg')}>
                Select Media
            </button>
        </div>
    ),
}));

// Mock shadcn/ui components
vi.mock('../../resources/js/components/ui/button', () => ({
    Button: ({ children, onClick, disabled, variant, type, ...props }: any) => (
        <button 
            onClick={onClick} 
            disabled={disabled} 
            data-variant={variant}
            type={type}
            {...props}
        >
            {children}
        </button>
    ),
}));

vi.mock('../../resources/js/components/ui/input', () => ({
    Input: React.forwardRef(({ value, onChange, type, min, ...props }: any, ref: any) => {
        const [inputValue, setInputValue] = React.useState(value || '');

        React.useEffect(() => {
            setInputValue(value || '');
        }, [value]);

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const newValue = type === 'number' ? 
                (e.target.value === '' ? 0 : parseInt(e.target.value) || 0) : 
                e.target.value;
            setInputValue(newValue);
            if (onChange) {
                const event = { ...e, target: { ...e.target, value: newValue } };
                onChange(event);
            }
        };

        return (
            <input
                ref={ref}
                type={type}
                value={inputValue}
                onChange={handleChange}
                min={min}
                {...props}
            />
        );
    }),
}));

vi.mock('../../resources/js/components/ui/label', () => ({
    Label: ({ children, htmlFor, ...props }: any) => (
        <label htmlFor={htmlFor} {...props}>{children}</label>
    ),
}));

vi.mock('../../resources/js/components/ui/switch', () => ({
    Switch: ({ checked, onCheckedChange, id, ...props }: any) => (
        <input
            id={id}
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange && onCheckedChange(e.target.checked)}
            data-testid={`switch-${id}`}
            {...props}
        />
    ),
}));

vi.mock('../../resources/js/components/ui/select', () => ({
    Select: ({ children, onValueChange, value, defaultValue }: any) => {
        const [selectValue, setSelectValue] = React.useState(value || defaultValue);

        React.useEffect(() => {
            setSelectValue(value || defaultValue);
        }, [value, defaultValue]);

        const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
            const newValue = e.target.value;
            setSelectValue(newValue);
            if (onValueChange) {
                onValueChange(newValue);
            }
        };

        return (
            <div className="select-wrapper">
                <select
                    role="combobox"
                    value={selectValue}
                    onChange={handleChange}
                    data-testid="select-dropdown"
                >
                    {children}
                </select>
            </div>
        );
    },
    SelectContent: ({ children }: any) => <>{children}</>,
    SelectItem: ({ value, children }: any) => <option value={value}>{children}</option>,
    SelectTrigger: ({ children }: any) => null, // Don't render trigger in test
    SelectValue: ({ placeholder }: any) => null, // Don't render value in test
}));

vi.mock('../../resources/js/components/ui/tabs', () => ({
    Tabs: ({ children, defaultValue }: any) => (
        <div data-testid="tabs" data-default-value={defaultValue}>
            {children}
        </div>
    ),
    TabsList: ({ children }: any) => (
        <div data-testid="tabs-list">{children}</div>
    ),
    TabsTrigger: ({ value, children }: any) => (
        <button data-testid={`tab-trigger-${value}`} data-value={value}>
            {children}
        </button>
    ),
    TabsContent: ({ value, children }: any) => (
        <div data-testid={`tab-content-${value}`} data-value={value}>
            {children}
        </div>
    ),
}));

vi.mock('../../resources/js/components/ui/card', () => ({
    Card: ({ children }: any) => <div data-testid="card">{children}</div>,
    CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
    CardDescription: ({ children }: any) => <div data-testid="card-description">{children}</div>,
    CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
    CardTitle: ({ children }: any) => <h3 data-testid="card-title">{children}</h3>,
}));

vi.mock('../../resources/js/components/ui/alert', () => ({
    Alert: ({ children }: any) => <div data-testid="alert">{children}</div>,
    AlertDescription: ({ children }: any) => <div data-testid="alert-description">{children}</div>,
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
    Search: () => <span data-testid="search-icon">Search</span>,
    Settings: () => <span data-testid="settings-icon">Settings</span>,
    AlertTriangle: () => <span data-testid="alert-triangle-icon">AlertTriangle</span>,
    Users: () => <span data-testid="users-icon">Users</span>,
    Activity: () => <span data-testid="activity-icon">Activity</span>,
    RefreshCw: () => <span data-testid="refresh-icon">RefreshCw</span>,
    Eye: () => <span data-testid="eye-icon">Eye</span>,
    EyeOff: () => <span data-testid="eye-off-icon">EyeOff</span>,
    TrendingUp: () => <span data-testid="trending-up-icon">TrendingUp</span>,
    Clock: () => <span data-testid="clock-icon">Clock</span>,
    Target: () => <span data-testid="target-icon">Target</span>,
    Image: () => <span data-testid="image-icon">Image</span>,
    Save: () => <span data-testid="save-icon">Save</span>,
    Shield: () => <span data-testid="shield-icon">Shield</span>,
}));

describe('SearchConfigurationIndex Component', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        mockRouterPost.mockClear();
        mockSetData.mockClear();
        mockUseForm.mockClear();
    });

    // Mock data that includes all categories including watermark and copy_protection
    const mockConfigurations = {
        guest_limits: {
            guest_search_limit: {
                key: 'guest_search_limit',
                value: 3,
                type: 'integer',
                description: 'Number of searches allowed for guest users',
                category: 'guest_limits',
            },
            guest_search_reset_hours: {
                key: 'guest_search_reset_hours',
                value: 24,
                type: 'integer',
                description: 'Hours after which guest search limit resets',
                category: 'guest_limits',
            },
        },
        display: {
            enable_partial_results: {
                key: 'enable_partial_results',
                value: true,
                type: 'boolean',
                description: 'Enable partial results with blur effect for guests',
                category: 'display',
            },
            guest_max_visible_results: {
                key: 'guest_max_visible_results',
                value: 5,
                type: 'integer',
                description: 'Maximum number of fully visible results for guests before blur effect',
                category: 'display',
            },
        },
        tracking: {
            track_guest_searches: {
                key: 'track_guest_searches',
                value: true,
                type: 'boolean',
                description: 'Track guest search queries for analytics',
                category: 'tracking',
            },
        },
        // These should be available for dedicated tabs but filtered from main Configuration tab
        watermark: {
            watermark_enabled: {
                key: 'watermark_enabled',
                value: false,
                type: 'boolean',
                description: 'Enable watermark system on search results',
                category: 'watermark',
            },
            watermark_text: {
                key: 'watermark_text',
                value: 'Mobile Parts DB',
                type: 'string',
                description: 'Fallback text watermark when no logo is provided',
                category: 'watermark',
            },
        },
        copy_protection: {
            copy_protection_enabled: {
                key: 'copy_protection_enabled',
                value: false,
                type: 'boolean',
                description: 'Enable copy protection system for search results',
                category: 'copy_protection',
            },
            copy_protection_level: {
                key: 'copy_protection_level',
                value: 'standard',
                type: 'string',
                description: 'Level of copy protection (basic, standard, strict)',
                category: 'copy_protection',
            },
        },
    };

    const mockStatistics = {
        guest_searches: {
            total_searches_today: 150,
            total_searches_week: 1200,
            unique_devices_today: 45,
            unique_devices_week: 320,
            searches_by_hour: [],
        },
        current_configs: {
            guest_search_limit: 3,
            search_reset_hours: 24,
            enable_partial_results: true,
            guest_max_visible_results: 5,
        },
        impact_metrics: {
            affected_guest_users: 500,
            conversion_rate: 0.125,
            average_searches_per_device: 2.3,
        },
    };

    const defaultProps = {
        configurations: mockConfigurations,
        statistics: mockStatistics,
    };

    test('renders search configuration page with correct layout', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check main layout elements
        expect(screen.getByTestId('app-layout')).toBeInTheDocument();
        expect(screen.getAllByText('Search Configuration')).toHaveLength(2); // One in breadcrumb, one in header
        expect(screen.getByText('Configure search limits, partial results, and tracking settings')).toBeInTheDocument();

        // Check breadcrumbs
        expect(screen.getByTestId('breadcrumbs')).toBeInTheDocument();
        expect(screen.getByTestId('breadcrumb-0')).toHaveTextContent('Admin');
        expect(screen.getByTestId('breadcrumb-1')).toHaveTextContent('Search Configuration');
    });

    test('renders all tab triggers correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check all tab triggers are present
        expect(screen.getByTestId('tab-trigger-configuration')).toBeInTheDocument();
        expect(screen.getByTestId('tab-trigger-watermark')).toBeInTheDocument();
        expect(screen.getByTestId('tab-trigger-copy-protection')).toBeInTheDocument();
        expect(screen.getByTestId('tab-trigger-statistics')).toBeInTheDocument();
        expect(screen.getByTestId('tab-trigger-impact')).toBeInTheDocument();

        // Check tab trigger text content
        expect(screen.getByTestId('tab-trigger-configuration')).toHaveTextContent('Configuration');
        expect(screen.getByTestId('tab-trigger-watermark')).toHaveTextContent('Watermark');
        expect(screen.getByTestId('tab-trigger-copy-protection')).toHaveTextContent('Copy Protection');
        expect(screen.getByTestId('tab-trigger-statistics')).toHaveTextContent('Statistics');
        expect(screen.getByTestId('tab-trigger-impact')).toHaveTextContent('Impact Analysis');
    });

    test('configuration tab shows only search-related categories', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check that search-related categories are present
        expect(screen.getByText('Guest User Limits')).toBeInTheDocument();
        expect(screen.getByText('Display Settings')).toBeInTheDocument();
        expect(screen.getByText('Search Tracking')).toBeInTheDocument();

        // Check that user limits are NOT present in main config (managed by subscription)
        expect(screen.queryByText('Authenticated User Limits')).not.toBeInTheDocument();

        // Check that watermark and copy protection categories are NOT in the main configuration tab
        // They should only appear in their dedicated tabs
        const configurationTabContent = screen.getByTestId('tab-content-configuration');
        expect(configurationTabContent).toBeInTheDocument();

        // These should not appear in the configuration tab content
        expect(configurationTabContent).not.toHaveTextContent('Watermark System');
        expect(configurationTabContent).not.toHaveTextContent('Copy Protection');
    });

    test('watermark tab shows watermark-specific content', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        const watermarkTabContent = screen.getByTestId('tab-content-watermark');
        expect(watermarkTabContent).toBeInTheDocument();

        // Check for watermark-specific elements
        expect(watermarkTabContent).toHaveTextContent('Basic Settings');
        expect(watermarkTabContent).toHaveTextContent('Enable Watermark System');
    });

    test('copy protection tab shows copy protection-specific content', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        const copyProtectionTabContent = screen.getByTestId('tab-content-copy-protection');
        expect(copyProtectionTabContent).toBeInTheDocument();

        // Check for copy protection-specific elements
        expect(copyProtectionTabContent).toHaveTextContent('Basic Settings');
        expect(copyProtectionTabContent).toHaveTextContent('Enable Copy Protection');
    });

    test('renders action buttons with correct functionality', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check main action buttons
        expect(screen.getByText('Test Configuration')).toBeInTheDocument();
        expect(screen.getByText('Reset to Defaults')).toBeInTheDocument();
        expect(screen.getByText('Save Configuration')).toBeInTheDocument();

        // Verify buttons are enabled
        expect(screen.getByText('Test Configuration')).toBeEnabled();
        expect(screen.getByText('Reset to Defaults')).toBeEnabled();
        expect(screen.getByText('Save Configuration')).toBeEnabled();
    });

    test('renders guest limits configuration correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check guest limits section
        expect(screen.getByText('Guest User Limits')).toBeInTheDocument();
        expect(screen.getByDisplayValue('3')).toBeInTheDocument(); // guest_search_limit
        expect(screen.getByDisplayValue('24')).toBeInTheDocument(); // guest_search_reset_hours
    });

    test('renders display settings with boolean switches', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check display settings section
        expect(screen.getByText('Display Settings')).toBeInTheDocument();
        expect(screen.getByDisplayValue('5')).toBeInTheDocument(); // guest_max_visible_results

        // Check boolean switch for partial results
        const partialResultsSwitch = screen.getByTestId('switch-enable_partial_results');
        expect(partialResultsSwitch).toBeInTheDocument();
        expect(partialResultsSwitch).toBeChecked();
    });

    test('renders tracking settings with boolean switches', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check tracking section
        expect(screen.getByText('Search Tracking')).toBeInTheDocument();

        // Check boolean switch for guest search tracking
        const trackingSwitch = screen.getByTestId('switch-track_guest_searches');
        expect(trackingSwitch).toBeInTheDocument();
        expect(trackingSwitch).toBeChecked();
    });

    test('handles numeric input changes correctly', async () => {
        const user = userEvent.setup();
        render(<SearchConfigurationIndex {...defaultProps} />);

        const guestLimitInput = screen.getByDisplayValue('3');
        await user.clear(guestLimitInput);
        await user.type(guestLimitInput, '5');

        expect(guestLimitInput).toHaveValue(5);
        expect(mockSetData).toHaveBeenCalledWith('guest_search_limit', 5);
    });

    test('handles boolean switch changes correctly', async () => {
        const user = userEvent.setup();
        render(<SearchConfigurationIndex {...defaultProps} />);

        const partialResultsSwitch = screen.getByTestId('switch-enable_partial_results');
        expect(partialResultsSwitch).toBeChecked();

        await user.click(partialResultsSwitch);
        expect(mockSetData).toHaveBeenCalledWith('enable_partial_results', false);
    });

    test('form submission calls correct endpoint with proper data structure', async () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        const saveButton = screen.getByText('Save Configuration');
        const form = saveButton.closest('form');
        expect(form).toBeInTheDocument();

        fireEvent.submit(form!);

        await waitFor(() => {
            expect(mockRouterPost).toHaveBeenCalledWith(
                '/admin/search-config/update',
                expect.objectContaining({
                    configurations: expect.arrayContaining([
                        expect.objectContaining({
                            key: expect.any(String),
                            value: expect.anything(),
                            type: expect.any(String),
                        }),
                    ]),
                }),
                expect.objectContaining({
                    onSuccess: expect.any(Function),
                    onError: expect.any(Function),
                    onFinish: expect.any(Function),
                })
            );
        });
    });

    test('test configuration button calls correct endpoint', async () => {
        const user = userEvent.setup();
        render(<SearchConfigurationIndex {...defaultProps} />);

        const testButton = screen.getByText('Test Configuration');
        await user.click(testButton);

        expect(mockRouterPost).toHaveBeenCalledWith(
            '/admin/search-config/test',
            expect.objectContaining({
                configurations: expect.any(Array),
            }),
            expect.objectContaining({
                onSuccess: expect.any(Function),
            })
        );
    });

    test('reset button shows confirmation dialog', async () => {
        const user = userEvent.setup();
        const mockConfirm = vi.spyOn(window, 'confirm').mockReturnValue(true);

        render(<SearchConfigurationIndex {...defaultProps} />);

        const resetButton = screen.getByText('Reset to Defaults');
        await user.click(resetButton);

        expect(mockConfirm).toHaveBeenCalledWith(
            'Are you sure you want to reset all configurations to defaults? This action cannot be undone.'
        );
        expect(mockRouterPost).toHaveBeenCalledWith('/admin/search-config/reset');

        mockConfirm.mockRestore();
    });

    test('reset confirmation can be cancelled', async () => {
        const user = userEvent.setup();
        const mockConfirm = vi.spyOn(window, 'confirm').mockReturnValue(false);

        render(<SearchConfigurationIndex {...defaultProps} />);

        const resetButton = screen.getByText('Reset to Defaults');
        await user.click(resetButton);

        expect(mockConfirm).toHaveBeenCalled();
        expect(mockRouterPost).not.toHaveBeenCalledWith('/admin/search-config/reset');

        mockConfirm.mockRestore();
    });

    test('displays statistics data correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check statistics values are displayed (these appear in the statistics tab)
        expect(screen.getByText('150')).toBeInTheDocument(); // total_searches_today
        expect(screen.getByText('1200')).toBeInTheDocument(); // total_searches_week
        // Note: 45 and 320 might be in different contexts, so we'll check for their presence more flexibly
        expect(screen.getByText(/45/)).toBeInTheDocument(); // unique_devices_today
        expect(screen.getByText(/320/)).toBeInTheDocument(); // unique_devices_week
    });

    test('displays impact metrics correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check impact metrics values
        expect(screen.getByText('500')).toBeInTheDocument(); // affected_guest_users
        expect(screen.getByText('12.5%')).toBeInTheDocument(); // conversion_rate (0.125 * 100)
        expect(screen.getByText('2.3')).toBeInTheDocument(); // average_searches_per_device
    });

    test('watermark configuration handles media picker', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        const watermarkTab = screen.getByTestId('tab-content-watermark');
        expect(watermarkTab).toBeInTheDocument();

        // Check if media picker is present in watermark tab
        const mediaPicker = screen.getByTestId('media-picker');
        expect(mediaPicker).toBeInTheDocument();
    });

    test('watermark save functionality works correctly', async () => {
        const user = userEvent.setup();
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Find save button in watermark tab
        const watermarkTab = screen.getByTestId('tab-content-watermark');
        const saveButton = within(watermarkTab).getByText('Save Watermark Settings');

        expect(saveButton).toBeInTheDocument();

        // Click save button should trigger form submission
        await user.click(saveButton);

        // Should call the same endpoint as main form
        await waitFor(() => {
            expect(mockRouterPost).toHaveBeenCalled();
        });
    });

    test('copy protection configuration renders correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        const copyProtectionTab = screen.getByTestId('tab-content-copy-protection');
        expect(copyProtectionTab).toBeInTheDocument();

        // Check for copy protection specific settings
        expect(copyProtectionTab).toHaveTextContent('Enable Copy Protection');
        expect(copyProtectionTab).toHaveTextContent('Basic Settings');
    });

    test('handles flash test results display', () => {
        const propsWithFlash = {
            ...defaultProps,
            flash: {
                test_results: {
                    warnings: ['Test warning message'],
                    recommendations: ['Test recommendation'],
                }
            }
        };

        render(<SearchConfigurationIndex {...propsWithFlash} />);

        // Check if test results alert is displayed
        expect(screen.getByTestId('alert')).toBeInTheDocument();
        expect(screen.getByText('⚠️ Test warning message')).toBeInTheDocument();
        expect(screen.getByText('✅ Test recommendation')).toBeInTheDocument();
    });

    test('category icons render correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check that category icons are rendered (there might be multiple instances across tabs)
        expect(screen.getAllByTestId('users-icon').length).toBeGreaterThanOrEqual(1); // guest_limits
        expect(screen.getAllByTestId('eye-icon').length).toBeGreaterThanOrEqual(1); // display
        expect(screen.getAllByTestId('activity-icon').length).toBeGreaterThanOrEqual(1); // tracking
    });

    test('form data initialization works correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Verify useForm was called (the exact structure may vary due to mocking)
        expect(mockUseForm).toHaveBeenCalled();

        // Verify the form instance has the expected data structure
        const formInstance = mockUseForm.mock.calls[0][0];
        expect(formInstance.data).toEqual(
            expect.objectContaining({
                guest_search_limit: 3,
                guest_search_reset_hours: 24,
                enable_partial_results: true,
                guest_max_visible_results: 5,
                track_guest_searches: true,
                watermark_enabled: false,
                watermark_text: 'Mobile Parts DB',
                copy_protection_enabled: false,
                copy_protection_level: 'standard',
            })
        );
    });

    test('handles string type configurations correctly', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check that string inputs are rendered in the watermark tab
        const watermarkTab = screen.getByTestId('tab-content-watermark');
        // Check for watermark-specific text that should be present
        expect(watermarkTab.textContent).toContain('Fallback Text');
        expect(watermarkTab.textContent).toContain('Text shown when no logo is available');
    });

    test('responsive design elements are present', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Check for responsive grid structure
        const cards = screen.getAllByTestId('card');
        expect(cards.length).toBeGreaterThan(0);

        // Check tabs structure
        expect(screen.getByTestId('tabs')).toBeInTheDocument();
        expect(screen.getByTestId('tabs-list')).toBeInTheDocument();
    });

    test('configuration categories are properly filtered', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        const configTab = screen.getByTestId('tab-content-configuration');

        // Should contain search-related categories
        expect(configTab).toHaveTextContent('Guest User Limits');
        expect(configTab).toHaveTextContent('Display Settings');
        expect(configTab).toHaveTextContent('Search Tracking');

        // Should NOT contain user limits, watermark or copy protection in main config tab
        expect(configTab).not.toHaveTextContent('Authenticated User Limits');
        expect(configTab).not.toHaveTextContent('Watermark System');
        expect(configTab).not.toHaveTextContent('Copy Protection');
    });

    test('error handling for invalid input values', async () => {
        const user = userEvent.setup();
        render(<SearchConfigurationIndex {...defaultProps} />);

        const guestLimitInput = screen.getByDisplayValue('3');

        // Try to enter invalid value
        await user.clear(guestLimitInput);
        await user.type(guestLimitInput, 'invalid');

        // Should default to 0 for invalid numeric input
        expect(guestLimitInput).toHaveValue(0);
    });

    test('component handles missing configuration gracefully', () => {
        const propsWithMissingConfig = {
            configurations: {
                guest_limits: {
                    guest_search_limit: {
                        key: 'guest_search_limit',
                        value: 3,
                        type: 'integer',
                        description: 'Number of searches allowed for guest users',
                        category: 'guest_limits',
                    },
                },
            },
            statistics: mockStatistics,
        };

        // Should not throw error with missing categories
        expect(() => {
            render(<SearchConfigurationIndex {...propsWithMissingConfig} />);
        }).not.toThrow();
    });
});
