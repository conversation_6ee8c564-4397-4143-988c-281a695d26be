import React from 'react';
import { render, screen, fireEvent, waitFor } from '../utils/test-utils';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import SearchConfigurationIndex from '@/pages/admin/SearchConfiguration/Index';
import { router } from '@inertiajs/react';

// Mock functions
const mockPost = vi.fn();
const mockUseForm = vi.fn();

// Mock Inertia
vi.mock('@inertiajs/react', () => {
    const mockPost = vi.fn();
    return {
        Head: ({ children }: any) => <head>{children}</head>,
        router: {
            post: mockPost,
        },
        usePage: () => ({
            props: {
                auth: {
                    user: { name: 'Test Admin' }
                }
            }
        }),
        useForm: () => ({
            data: {},
            setData: vi.fn(),
            post: vi.fn(),
            processing: false,
            reset: vi.fn(),
        }),
    };
});

// Mock UI components
vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, ...props }: any) => (
        <button onClick={onClick} {...props}>{children}</button>
    ),
}));

vi.mock('@/components/ui/input', () => ({
    Input: ({ value, onChange, ...props }: any) => (
        <input 
            value={value || ''} 
            onChange={(e) => onChange?.(e)} 
            {...props} 
        />
    ),
}));

vi.mock('@/components/ui/switch', () => ({
    Switch: ({ checked, onCheckedChange, ...props }: any) => (
        <input 
            type="checkbox" 
            checked={checked} 
            onChange={(e) => onCheckedChange?.(e.target.checked)} 
            {...props} 
        />
    ),
}));

vi.mock('@/components/ui/card', () => ({
    Card: ({ children }: any) => <div data-testid="card">{children}</div>,
    CardHeader: ({ children }: any) => <div>{children}</div>,
    CardTitle: ({ children }: any) => <h3>{children}</h3>,
    CardDescription: ({ children }: any) => <p>{children}</p>,
    CardContent: ({ children }: any) => <div>{children}</div>,
}));

vi.mock('@/components/ui/tabs', () => ({
    Tabs: ({ children }: any) => <div data-testid="tabs">{children}</div>,
    TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
    TabsTrigger: ({ children, value }: any) => <button data-testid={`tab-${value}`}>{children}</button>,
    TabsContent: ({ children }: any) => <div>{children}</div>,
}));

vi.mock('@/components/ui/label', () => ({
    Label: ({ children }: any) => <label>{children}</label>,
}));

vi.mock('@/components/ui/alert', () => ({
    Alert: ({ children }: any) => <div data-testid="alert">{children}</div>,
    AlertDescription: ({ children }: any) => <div>{children}</div>,
}));

vi.mock('@/components/ui/select', () => ({
    Select: ({ children }: any) => <div data-testid="select">{children}</div>,
    SelectContent: ({ children }: any) => <div>{children}</div>,
    SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
    SelectTrigger: ({ children }: any) => <div>{children}</div>,
    SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

vi.mock('lucide-react', () => ({
    Activity: () => <span>Activity Icon</span>,
    RefreshCw: () => <span>Refresh Icon</span>,
    Users: () => <span>Users Icon</span>,
    Eye: () => <span>Eye Icon</span>,
    EyeOff: () => <span>EyeOff Icon</span>,
    BarChart: () => <span>Chart Icon</span>,
    Shield: () => <span>Shield Icon</span>,
    Image: () => <span>Image Icon</span>,
    Settings: () => <span>Settings Icon</span>,
    Search: () => <span>Search Icon</span>,
    TrendingUp: () => <span>TrendingUp Icon</span>,
    AlertTriangle: () => <span>AlertTriangle Icon</span>,
    Clock: () => <span>Clock Icon</span>,
    Target: () => <span>Target Icon</span>,
    Save: () => <span>Save Icon</span>,
}));

vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: any) => <div data-testid="app-layout">{children}</div>,
}));

vi.mock('@/components/MediaPicker', () => ({
    default: ({ onSelect, isOpen, acceptedTypes }: any) => (
        <div data-testid="media-picker">
            {isOpen && <div>Media Picker Open</div>}
            <button onClick={() => onSelect?.('test-url')}>Select Media</button>
        </div>
    ),
}));

describe('SearchConfigurationIndex - Null Value Handling', () => {
    let consoleWarnSpy: any;
    let consoleLogSpy: any;
    let consoleErrorSpy: any;

    const mockFormInstance = {
        data: {},
        setData: vi.fn(),
        post: vi.fn(),
        processing: false,
        reset: vi.fn(),
    };

    const configurationsWithNullValues = {
        guest_limits: {
            guest_search_limit: {
                key: 'guest_search_limit',
                value: 3,
                type: 'integer',
                description: 'Number of searches allowed for guest users',
                category: 'guest_limits',
            },
        },
        watermark: {
            watermark_enabled: {
                key: 'watermark_enabled',
                value: false,
                type: 'boolean',
                description: 'Enable watermark on search results',
                category: 'watermark',
            },
            watermark_logo_url: {
                key: 'watermark_logo_url',
                value: null, // This is the problematic null value
                type: 'string',
                description: 'URL for watermark logo',
                category: 'watermark',
            },
            watermark_text: {
                key: 'watermark_text',
                value: '',
                type: 'string',
                description: 'Watermark text',
                category: 'watermark',
            },
        },
    };

    const defaultProps = {
        configurations: configurationsWithNullValues,
        statistics: {
            total_searches: 100,
            guest_searches: 50,
            user_searches: 50,
            current_configs: {
                search_reset_hours: 24,
                guest_search_limit: 3,
                free_user_daily_limit: 20,
                premium_user_daily_limit: -1,
            },
            impact_metrics: {
                affected_guest_users: 0,
                conversion_rate: 0,
                average_searches_per_device: 0,
            },
        },
    };

    beforeEach(() => {
        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
        consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
        consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        mockUseForm.mockReturnValue(mockFormInstance);
        vi.mocked(router.post).mockClear();
    });

    afterEach(() => {
        consoleWarnSpy.mockRestore();
        consoleLogSpy.mockRestore();
        consoleErrorSpy.mockRestore();
    });

    it('handles null configuration values gracefully', () => {
        render(<SearchConfigurationIndex {...defaultProps} />);

        // Should warn about null value and provide default
        expect(consoleWarnSpy).toHaveBeenCalledWith(
            'Configuration watermark_logo_url has null/undefined value, using default:',
            ''
        );

        // Component should render without crashing
        expect(screen.getByTestId('app-layout')).toBeInTheDocument();
    });

    it('provides appropriate defaults based on configuration type', () => {
        const configurationsWithVariousNulls = {
            test: {
                test_string: {
                    key: 'test_string',
                    value: null,
                    type: 'string',
                    description: 'Test string',
                    category: 'test',
                },
                test_integer: {
                    key: 'test_integer',
                    value: null,
                    type: 'integer',
                    description: 'Test integer',
                    category: 'test',
                },
                test_boolean: {
                    key: 'test_boolean',
                    value: null,
                    type: 'boolean',
                    description: 'Test boolean',
                    category: 'test',
                },
                test_float: {
                    key: 'test_float',
                    value: null,
                    type: 'float',
                    description: 'Test float',
                    category: 'test',
                },
                premium_user_daily_limit: {
                    key: 'premium_user_daily_limit',
                    value: null,
                    type: 'integer',
                    description: 'Premium user limit',
                    category: 'test',
                },
            },
        };

        render(<SearchConfigurationIndex 
            configurations={configurationsWithVariousNulls} 
            statistics={defaultProps.statistics} 
        />);

        // Should provide appropriate defaults for each type
        expect(consoleWarnSpy).toHaveBeenCalledWith(
            'Configuration test_string has null/undefined value, using default:',
            ''
        );
        expect(consoleWarnSpy).toHaveBeenCalledWith(
            'Configuration test_integer has null/undefined value, using default:',
            0
        );
        expect(consoleWarnSpy).toHaveBeenCalledWith(
            'Configuration test_boolean has null/undefined value, using default:',
            false
        );
        expect(consoleWarnSpy).toHaveBeenCalledWith(
            'Configuration test_float has null/undefined value, using default:',
            0.0
        );
        expect(consoleWarnSpy).toHaveBeenCalledWith(
            'Configuration premium_user_daily_limit has null/undefined value, using default:',
            -1
        );
    });

    it('handles form submission with null values', async () => {
        const mockSetData = vi.fn();
        mockFormInstance.setData = mockSetData;
        mockFormInstance.data = {
            guest_search_limit: 3,
            watermark_enabled: false,
            watermark_logo_url: null,
            watermark_text: 'Test',
        };

        render(<SearchConfigurationIndex {...defaultProps} />);

        const saveButton = screen.getByText('Save Configuration');
        fireEvent.click(saveButton);

        await waitFor(() => {
            expect(router.post).toHaveBeenCalled();
        });

        // Should log detailed information about configurations being sent
        // The second call should have the actual configurations
        expect(consoleLogSpy).toHaveBeenNthCalledWith(2,
            'Sending configurations:',
            expect.objectContaining({
                count: expect.any(Number),
                configurations: expect.any(Array)
            })
        );
    });

    it('provides detailed error logging when submission fails', async () => {
        const mockOnError = vi.fn();
        vi.mocked(router.post).mockImplementation((url, data, options) => {
            options.onError({
                'configurations.12.value': 'The configurations.12.value field is required.'
            });
        });

        mockFormInstance.data = {
            guest_search_limit: 3,
            watermark_enabled: false,
            watermark_logo_url: null,
            watermark_text: 'Test',
        };

        render(<SearchConfigurationIndex {...defaultProps} />);

        const saveButton = screen.getByText('Save Configuration');
        fireEvent.click(saveButton);

        await waitFor(() => {
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                'Configuration save failed:',
                expect.objectContaining({
                    errors: expect.any(Object),
                    configurationsCount: expect.any(Number),
                })
            );
        });

        // The test should just verify that error logging occurs, not the specific format
        // since the component may not have exactly 12 configurations in our mock data
    });
});
