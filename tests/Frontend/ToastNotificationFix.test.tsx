import { render, screen, waitFor } from '../utils/test-utils';
import { router } from '@inertiajs/react';
import { toast } from 'sonner';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import FlashMessageHandler from '@/components/flash-message-handler';
import { usePage } from '@inertiajs/react';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
    router: {
        post: vi.fn(),
        reload: vi.fn(),
    },
    usePage: vi.fn(),
}));

vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
        warning: vi.fn(),
        info: vi.fn(),
    },
}));

describe('Toast Notification Fix', () => {
    const mockUsePage = usePage as any;
    const mockToast = toast as any;
    const mockRouter = router as any;

    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('FlashMessageHandler', () => {
        it('should display success toast for flash success message', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Compatibility import completed successfully. Updated: 1 compatibility records.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Compatibility import completed successfully. Updated: 1 compatibility records.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should display error toast for flash error message', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        error: 'Import failed. Please check your CSV file.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(
                    'Import failed. Please check your CSV file.'
                );
                expect(mockToast.error).toHaveBeenCalledTimes(1);
            });
        });

        it('should display warning toast for flash warning message', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        warning: 'Import completed but 2 rows had issues. Check console for details.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.warning).toHaveBeenCalledWith(
                    'Import completed but 2 rows had issues. Check console for details.'
                );
                expect(mockToast.warning).toHaveBeenCalledTimes(1);
            });
        });

        it('should not display any toast when no flash messages', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {},
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).not.toHaveBeenCalled();
                expect(mockToast.error).not.toHaveBeenCalled();
                expect(mockToast.warning).not.toHaveBeenCalled();
                expect(mockToast.info).not.toHaveBeenCalled();
            });
        });

        it('should handle multiple flash message types', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Operation completed successfully.',
                        warning: 'Some warnings occurred.',
                        info: 'Additional information.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith('Operation completed successfully.');
                expect(mockToast.warning).toHaveBeenCalledWith('Some warnings occurred.');
                expect(mockToast.info).toHaveBeenCalledWith('Additional information.');
                expect(mockToast.success).toHaveBeenCalledTimes(1);
                expect(mockToast.warning).toHaveBeenCalledTimes(1);
                expect(mockToast.info).toHaveBeenCalledTimes(1);
            });
        });
    });

    describe('Import Operations - No Duplicate Toasts', () => {
        it('should not call toast.success in compatibility import onSuccess handler', () => {
            // Mock a successful compatibility import response
            const mockPage = {
                props: {
                    flash: {
                        success: 'Compatibility import completed successfully. Updated: 1 compatibility records.',
                    },
                },
            };

            // Simulate the onSuccess handler behavior after our fix
            const onSuccessHandler = (page: any) => {
                console.log('Import successful!', page);
                // Flash message will be handled by FlashMessageHandler component
                // No toast.success call here anymore
            };

            // Call the handler
            onSuccessHandler(mockPage);

            // Verify that toast.success was not called in the handler
            expect(mockToast.success).not.toHaveBeenCalled();
        });

        it('should not call toast.success in bulk import onSuccess handlers', () => {
            // Mock a successful bulk import response
            const mockPage = {
                props: {
                    flash: {
                        success: 'Imported 5 new brands successfully.',
                    },
                },
            };

            // Simulate the onSuccess handler behavior after our fix
            const onSuccessHandler = () => {
                console.log('Brands import successful');
                // Flash message will be handled by FlashMessageHandler component
                // No toast.success call here anymore
            };

            // Call the handler
            onSuccessHandler();

            // Verify that toast.success was not called in the handler
            expect(mockToast.success).not.toHaveBeenCalled();
        });
    });

    describe('Error Handling - Still Works', () => {
        it('should still call toast.error for client-side errors', () => {
            // Simulate an error handler that should still work
            const onErrorHandler = (errors: any) => {
                if (errors.file) {
                    toast.error(`File error: ${errors.file}`);
                } else {
                    toast.error('Import failed. Please check your CSV format and try again.');
                }
            };

            // Test with file error
            onErrorHandler({ file: 'Invalid file format' });
            expect(mockToast.error).toHaveBeenCalledWith('File error: Invalid file format');

            // Test with generic error
            vi.clearAllMocks();
            onErrorHandler({});
            expect(mockToast.error).toHaveBeenCalledWith('Import failed. Please check your CSV format and try again.');
        });
    });
});
