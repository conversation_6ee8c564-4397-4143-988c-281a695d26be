import React from 'react';
import { render, screen, fireEvent } from '../utils/test-utils';
import '@testing-library/jest-dom';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import ColumnSelectionTable, { ColumnInfo } from '@/components/ColumnSelectionTable';

const mockColumns: ColumnInfo[] = [
    {
        name: 'Brand',
        sampleData: ['Apple', 'Samsung', 'Xiaomi'],
        isRequired: true,
        mappedTo: 'Brand',
        purpose: 'Device manufacturer',
        isSelected: true
    },
    {
        name: 'Model',
        sampleData: ['iPhone 13', 'Galaxy S21', 'Redmi Note 9'],
        isRequired: true,
        mappedTo: 'Model',
        purpose: 'Device model name',
        isSelected: true
    },
    {
        name: 'Compatible',
        sampleData: ['true', 'false'],
        isRequired: true,
        mappedTo: 'Compatible',
        purpose: 'Compatibility status',
        isSelected: true
    },
    {
        name: 'Display Type',
        sampleData: ['OLED', 'AMOLED', 'LCD'],
        isRequired: false,
        mappedTo: 'Display Type',
        purpose: 'Display technology',
        isSelected: true
    },
    {
        name: 'Notes',
        sampleData: ['Test note', 'Another note'],
        isRequired: false,
        mappedTo: 'Notes',
        purpose: 'Additional notes',
        isSelected: false
    }
];

describe('ColumnSelectionTable', () => {
    const mockOnColumnToggle = vi.fn();
    const mockOnSelectAll = vi.fn();
    const mockOnDeselectAll = vi.fn();

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders column information correctly', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        // Check that all columns are displayed (they appear in both quick selection and details)
        expect(screen.getAllByText('Brand')).toHaveLength(2);
        expect(screen.getAllByText('Model')).toHaveLength(2);
        expect(screen.getAllByText('Compatible')).toHaveLength(2);
        expect(screen.getAllByText('Display Type')).toHaveLength(2);
        expect(screen.getAllByText('Notes')).toHaveLength(2);

        // Check sample data
        expect(screen.getByText('Apple')).toBeInTheDocument();
        expect(screen.getByText('iPhone 13')).toBeInTheDocument();
        expect(screen.getByText('OLED')).toBeInTheDocument();

        // Check purposes
        expect(screen.getByText('Device manufacturer')).toBeInTheDocument();
        expect(screen.getByText('Device model name')).toBeInTheDocument();
        expect(screen.getByText('Display technology')).toBeInTheDocument();
    });

    it('shows required column indicators', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        // Required columns should have asterisks
        const requiredIndicators = screen.getAllByText('*');
        expect(requiredIndicators).toHaveLength(6); // 3 required columns × 2 (quick selection + details section)
    });

    it('renders quick column selection row with checkboxes', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        // Check if quick selection section exists
        const quickSelectionContainer = document.querySelector('.bg-muted\\/30');
        expect(quickSelectionContainer).toBeInTheDocument();

        // Check if all column names appear in quick selection
        expect(quickSelectionContainer).toHaveTextContent('Brand');
        expect(quickSelectionContainer).toHaveTextContent('Model');
        expect(quickSelectionContainer).toHaveTextContent('Compatible');
        expect(quickSelectionContainer).toHaveTextContent('Display Type');
        expect(quickSelectionContainer).toHaveTextContent('Notes');

        // Check that checkboxes are present for each column in quick selection
        const quickSelectionCheckboxes = quickSelectionContainer?.querySelectorAll('input[type="checkbox"]');
        expect(quickSelectionCheckboxes).toHaveLength(mockColumns.length);
    });

    it('displays selection summary correctly', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        expect(screen.getByText('4 of 5 columns selected')).toBeInTheDocument();
        expect(screen.getByText('3 of 3 required columns selected')).toBeInTheDocument();
    });

    it('calls onColumnToggle when checkbox is clicked', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        // Find and click the Notes checkbox (it's not selected)
        const checkboxes = screen.getAllByRole('checkbox');
        const notesCheckbox = checkboxes.find(checkbox => 
            !checkbox.getAttribute('checked') && 
            checkbox.closest('[class*="col-span"]')?.textContent?.includes('Notes')
        );

        if (notesCheckbox) {
            fireEvent.click(notesCheckbox);
            expect(mockOnColumnToggle).toHaveBeenCalledWith('Notes', true);
        }
    });

    it('calls onSelectAll when Select All button is clicked', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        const selectAllButton = screen.getByText('Select All');
        fireEvent.click(selectAllButton);
        expect(mockOnSelectAll).toHaveBeenCalled();
    });

    it('calls onDeselectAll when Deselect All button is clicked', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        const deselectAllButton = screen.getByText('Deselect All');
        fireEvent.click(deselectAllButton);
        expect(mockOnDeselectAll).toHaveBeenCalled();
    });

    it('disables required column checkboxes when selected', () => {
        render(
            <ColumnSelectionTable
                columns={mockColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        const checkboxes = screen.getAllByRole('checkbox');
        
        // Required columns that are selected should be disabled
        const brandCheckboxes = checkboxes.filter(checkbox => 
            checkbox.getAttribute('checked') !== null &&
            checkbox.closest('[class*="grid"]')?.textContent?.includes('Brand')
        );
        
        brandCheckboxes.forEach(checkbox => {
            expect(checkbox).toBeDisabled();
        });
    });

    it('shows warning when required columns are missing', () => {
        const columnsWithMissingRequired = mockColumns.map(col => 
            col.name === 'Compatible' ? { ...col, isSelected: false } : col
        );

        render(
            <ColumnSelectionTable
                columns={columnsWithMissingRequired}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        expect(screen.getByText(/Warning:/)).toBeInTheDocument();
        expect(screen.getByText(/You must select all required columns/)).toBeInTheDocument();
    });

    it('disables Select All button when all columns are selected', () => {
        const allSelectedColumns = mockColumns.map(col => ({ ...col, isSelected: true }));

        render(
            <ColumnSelectionTable
                columns={allSelectedColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        const selectAllButton = screen.getByText('Select All');
        expect(selectAllButton).toBeDisabled();
    });

    it('disables Deselect All button when no columns are selected', () => {
        const noSelectedColumns = mockColumns.map(col => ({ ...col, isSelected: false }));

        render(
            <ColumnSelectionTable
                columns={noSelectedColumns}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        const deselectAllButton = screen.getByText('Deselect All');
        expect(deselectAllButton).toBeDisabled();
    });

    it('truncates long sample data', () => {
        const columnsWithLongData = [{
            ...mockColumns[0],
            sampleData: ['This is a very long sample data that should be truncated']
        }];

        render(
            <ColumnSelectionTable
                columns={columnsWithLongData}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        expect(screen.getByText(/This is a very long.../)).toBeInTheDocument();
    });

    it('shows mapped column names when different from original', () => {
        const columnsWithMapping = [{
            ...mockColumns[0],
            name: 'brand_name',
            mappedTo: 'Brand'
        }];

        render(
            <ColumnSelectionTable
                columns={columnsWithMapping}
                onColumnToggle={mockOnColumnToggle}
                onSelectAll={mockOnSelectAll}
                onDeselectAll={mockOnDeselectAll}
            />
        );

        expect(screen.getAllByText('brand_name')).toHaveLength(2); // Appears in both quick selection and details
        expect(screen.getByText('Maps to: Brand')).toBeInTheDocument();
    });
});
