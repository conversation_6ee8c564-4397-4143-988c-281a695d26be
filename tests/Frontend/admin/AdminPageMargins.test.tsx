import { render, screen } from '../../utils/test-utils';
import { describe, it, expect, vi } from 'vitest';
import { Head } from '@inertiajs/react';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    useForm: () => ({
        data: {
            title: '',
            slug: '',
            content: '',
            featured_image: '',
            meta_description: '',
            meta_keywords: '',
            layout: 'default',
            is_published: false,
            published_at: '',
        },
        setData: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        processing: false,
        errors: {},
        reset: vi.fn(),
    }),
    router: {
        get: vi.fn(),
        delete: vi.fn(),
        reload: vi.fn(),
    },
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock UI components
vi.mock('@/components/ui/card', () => ({
    Card: ({ children, className }: any) => <div className={className} data-testid="card">{children}</div>,
    CardContent: ({ children, className }: any) => <div className={className} data-testid="card-content">{children}</div>,
    CardDescription: ({ children, className }: any) => <div className={className} data-testid="card-description">{children}</div>,
    CardHeader: ({ children, className }: any) => <div className={className} data-testid="card-header">{children}</div>,
    CardTitle: ({ children, className }: any) => <div className={className} data-testid="card-title">{children}</div>,
    CardFooter: ({ children, className }: any) => <div className={className} data-testid="card-footer">{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
    Button: ({ children, className, ...props }: any) => <button className={className} {...props}>{children}</button>,
}));

vi.mock('@/components/ui/input', () => ({
    Input: (props: any) => <input {...props} />,
}));

vi.mock('@/components/ui/label', () => ({
    Label: ({ children, ...props }: any) => <label {...props}>{children}</label>,
}));

vi.mock('@/components/ui/textarea', () => ({
    Textarea: (props: any) => <textarea {...props} />,
}));

vi.mock('@/components/ui/select', () => ({
    Select: ({ children }: any) => <div data-testid="select">{children}</div>,
    SelectContent: ({ children }: any) => <div data-testid="select-content">{children}</div>,
    SelectItem: ({ children, value }: any) => <div data-testid="select-item" data-value={value}>{children}</div>,
    SelectTrigger: ({ children, className }: any) => <div className={className} data-testid="select-trigger">{children}</div>,
    SelectValue: ({ placeholder }: any) => <div data-testid="select-value">{placeholder}</div>,
}));

vi.mock('@/components/ui/switch', () => ({
    Switch: (props: any) => <input type="checkbox" {...props} data-testid="switch" />,
}));

vi.mock('@/components/ui/separator', () => ({
    Separator: () => <hr data-testid="separator" />,
}));

vi.mock('@/components/ui/badge', () => ({
    Badge: ({ children, className }: any) => <span className={className} data-testid="badge">{children}</span>,
}));

vi.mock('@/components/ui/table', () => ({
    Table: ({ children }: any) => <table data-testid="table">{children}</table>,
    TableBody: ({ children }: any) => <tbody data-testid="table-body">{children}</tbody>,
    TableCell: ({ children, className }: any) => <td className={className} data-testid="table-cell">{children}</td>,
    TableHead: ({ children, className }: any) => <th className={className} data-testid="table-head">{children}</th>,
    TableHeader: ({ children }: any) => <thead data-testid="table-header">{children}</thead>,
    TableRow: ({ children }: any) => <tr data-testid="table-row">{children}</tr>,
}));

vi.mock('@/components/ui/tabs', () => ({
    Tabs: ({ children }: any) => <div data-testid="tabs">{children}</div>,
    TabsContent: ({ children }: any) => <div data-testid="tabs-content">{children}</div>,
    TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
    TabsTrigger: ({ children }: any) => <div data-testid="tabs-trigger">{children}</div>,
}));

// Mock other components
vi.mock('@/components/pagination', () => ({
    Pagination: () => <div data-testid="pagination" />,
}));

vi.mock('@/components/SimpleMediaPicker', () => ({
    SimpleMediaPicker: () => <div data-testid="simple-media-picker" />,
}));

vi.mock('@/hooks/use-delete-confirmation', () => ({
    useDeleteConfirmation: () => ({
        confirmDelete: vi.fn(),
        showDeleteConfirmation: vi.fn(),
    }),
}));

vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

vi.mock('@/lib/utils', () => ({
    formatDate: (date: string) => new Date(date).toLocaleDateString(),
    cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

// Import components to test
import PagesCreate from '@/pages/admin/Pages/Create';
import PagesEdit from '@/pages/admin/Pages/Edit';
import PagesIndex from '@/pages/admin/Pages/Index';
import MenusCreate from '@/pages/admin/Menus/Create';
import MenusEdit from '@/pages/admin/Menus/Edit';
import MenusIndex from '@/pages/admin/Menus/Index';
import MenusShow from '@/pages/admin/Menus/Show';

describe('Admin Page Margins', () => {
    const mockLayouts = {
        default: 'Default Layout',
        full_width: 'Full Width Layout',
    };

    const mockLocations = {
        header: 'Header',
        footer: 'Footer',
    };

    const mockPage = {
        id: 1,
        title: 'Test Page',
        slug: 'test-page',
        content: 'Test content',
        featured_image: null,
        meta_description: null,
        meta_keywords: null,
        layout: 'default',
        is_published: true,
        author_id: 1,
        published_at: '2024-01-01T00:00:00Z',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        author: { id: 1, name: 'Test Author', email: '<EMAIL>' },
        url: '/page/test-page',
    };

    const mockMenu = {
        id: 1,
        name: 'Test Menu',
        location: 'header',
        description: 'Test description',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: [],
    };

    describe('Pages Components', () => {
        it('should use p-6 space-y-6 margin pattern in Create component', () => {
            render(<PagesCreate layouts={mockLayouts} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });

        it('should use p-6 space-y-6 margin pattern in Edit component', () => {
            render(<PagesEdit page={mockPage} layouts={mockLayouts} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });

        it('should use p-6 space-y-6 margin pattern in Index component', () => {
            const mockPages = {
                data: [],
                current_page: 1,
                last_page: 1,
                per_page: 10,
                total: 0,
                from: 0,
                to: 0,
            };

            const mockFilters = {
                search: null,
                status: null,
                layout: null,
            };

            render(<PagesIndex pages={mockPages} filters={mockFilters} layouts={mockLayouts} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });
    });

    describe('Menus Components', () => {
        it('should use p-6 space-y-6 margin pattern in Create component', () => {
            render(<MenusCreate locations={mockLocations} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });

        it('should use p-6 space-y-6 margin pattern in Edit component', () => {
            render(<MenusEdit menu={mockMenu} locations={mockLocations} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });

        it('should use p-6 space-y-6 margin pattern in Index component', () => {
            const mockMenus = {
                data: [],
                current_page: 1,
                last_page: 1,
                per_page: 10,
                total: 0,
                from: 0,
                to: 0,
            };

            const mockFilters = {
                search: null,
                location: null,
            };

            render(<MenusIndex menus={mockMenus} filters={mockFilters} locations={mockLocations} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });

        it('should use p-6 space-y-6 margin pattern in Show component', () => {
            const mockAvailableItems = {
                pages: [],
                categories: [],
                brands: [],
                models: [],
            };

            const mockItemTypes = {
                custom: 'Custom Link',
                page: 'Page',
            };

            const mockTargetOptions = {
                _self: 'Same Window',
                _blank: 'New Window',
            };

            render(
                <MenusShow 
                    menu={mockMenu} 
                    availableItems={mockAvailableItems}
                    itemTypes={mockItemTypes}
                    targetOptions={mockTargetOptions}
                />
            );
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // Should not use container class
            const containerElement = document.querySelector('.container');
            expect(containerElement).not.toBeInTheDocument();
        });
    });

    describe('Responsive Design', () => {
        it('should maintain proper spacing on different screen sizes', () => {
            render(<PagesCreate layouts={mockLayouts} />);
            
            const marginContainer = document.querySelector('.p-6.space-y-6');
            expect(marginContainer).toBeInTheDocument();
            
            // p-6 provides consistent padding on all screen sizes
            // space-y-6 provides consistent vertical spacing between elements
            expect(marginContainer).toHaveClass('p-6');
            expect(marginContainer).toHaveClass('space-y-6');
        });
    });
});
