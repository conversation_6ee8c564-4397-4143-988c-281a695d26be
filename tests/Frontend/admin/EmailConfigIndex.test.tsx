import React from 'react';
import { render, screen, fireEvent, waitFor, userEvent } from '../../utils/test-utils';
import { router } from '@inertiajs/react';
import EmailConfigIndex from '@/pages/admin/EmailConfig/Index';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    router: {
        post: vi.fn(),
    },
    useForm: () => ({
        data: {
            provider: 'smtp',
            from_address: '<EMAIL>',
            from_name: 'Test App',
            smtp_host: 'smtp.gmail.com',
            smtp_port: 587,
            smtp_username: '<EMAIL>',
            smtp_password: '',
            smtp_encryption: 'tls',
            sendgrid_api_key: '',
        },
        setData: vi.fn(),
        post: vi.fn(),
        processing: false,
        errors: {},
    }),
    Head: ({ title }: { title: string }) => <title>{title}</title>,
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

describe('EmailConfigIndex', () => {
    const mockProps = {
        config: {
            default_provider: 'smtp',
            from_address: '<EMAIL>',
            from_name: 'Test App',
            smtp: {
                host: 'smtp.gmail.com',
                port: 587,
                username: '<EMAIL>',
                encryption: 'tls',
                password_set: true,
            },
            sendgrid: {
                api_key_set: false,
                api_key_prefix: null,
            },
        },
        provider_status: {
            provider: 'smtp',
            configured: true,
            status: 'healthy',
        },
        email_stats: {
            total_sent: 150,
            total_delivered: 145,
            total_bounced: 5,
            total_opened: 120,
            total_clicked: 80,
            delivery_rate: 96.7,
            open_rate: 82.8,
            click_rate: 66.7,
            bounce_rate: 3.3,
            provider: 'smtp',
            period_days: 30,
        },
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders email configuration page correctly', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        expect(screen.getByText('Email Configuration')).toBeInTheDocument();
        expect(screen.getByText('Configure email providers and manage email delivery settings')).toBeInTheDocument();
    });

    it('displays current email statistics', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        expect(screen.getByText('150')).toBeInTheDocument(); // total_sent
        expect(screen.getByText('96.7%')).toBeInTheDocument(); // delivery_rate
        expect(screen.getByText('82.8%')).toBeInTheDocument(); // open_rate
    });

    it('shows current provider status', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        expect(screen.getByText('smtp')).toBeInTheDocument();
        expect(screen.getByText('Healthy')).toBeInTheDocument();
    });

    it('displays configuration form with current values', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test App')).toBeInTheDocument();
    });

    it('shows SMTP configuration fields when SMTP is selected', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        // Should show SMTP-specific fields
        expect(screen.getByDisplayValue('smtp.gmail.com')).toBeInTheDocument();
        expect(screen.getByDisplayValue('587')).toBeInTheDocument();
        expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });

    it('has working tab navigation', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        const settingsTab = screen.getByRole('tab', { name: 'Settings' });
        const testEmailTab = screen.getByRole('tab', { name: 'Test Email' });
        const statisticsTab = screen.getByRole('tab', { name: 'Statistics' });
        
        expect(settingsTab).toBeInTheDocument();
        expect(testEmailTab).toBeInTheDocument();
        expect(statisticsTab).toBeInTheDocument();
        
        // Settings tab should be selected by default
        expect(settingsTab).toHaveAttribute('aria-selected', 'true');
    });

    it('switches to test email tab when clicked', async () => {
        const user = userEvent.setup();
        render(<EmailConfigIndex {...mockProps} />);

        const testEmailTab = screen.getByRole('tab', { name: 'Test Email' });
        await user.click(testEmailTab);

        await waitFor(() => {
            expect(testEmailTab).toHaveAttribute('aria-selected', 'true');
        });
        expect(screen.getByRole('button', { name: /send test email/i })).toBeInTheDocument();
    });

    it('switches to statistics tab when clicked', async () => {
        const user = userEvent.setup();
        render(<EmailConfigIndex {...mockProps} />);

        const statisticsTab = screen.getByRole('tab', { name: 'Statistics' });
        await user.click(statisticsTab);

        await waitFor(() => {
            expect(statisticsTab).toHaveAttribute('aria-selected', 'true');
        });
        expect(screen.getByText('Email Performance')).toBeInTheDocument();
    });

    it('displays test config button', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        const testConfigButton = screen.getByRole('button', { name: 'Test Config' });
        expect(testConfigButton).toBeInTheDocument();
    });

    it('calls router.post when test config button is clicked', async () => {
        const mockRouterPost = vi.fn();
        (router.post as any) = mockRouterPost;
        
        render(<EmailConfigIndex {...mockProps} />);
        
        const testConfigButton = screen.getByRole('button', { name: 'Test Config' });
        fireEvent.click(testConfigButton);
        
        await waitFor(() => {
            expect(mockRouterPost).toHaveBeenCalledWith(
                '/admin/email-config/test',
                { provider: 'smtp' },
                expect.any(Object)
            );
        });
    });

    it('shows save configuration button', () => {
        render(<EmailConfigIndex {...mockProps} />);
        
        const saveButton = screen.getByRole('button', { name: 'Save Configuration' });
        expect(saveButton).toBeInTheDocument();
    });

    it('handles error state gracefully', () => {
        const propsWithError = {
            ...mockProps,
            error: 'There was an issue loading email configuration. Please check the logs for details.',
        };
        
        render(<EmailConfigIndex {...propsWithError} />);
        
        // Should still render the page
        expect(screen.getByText('Email Configuration')).toBeInTheDocument();
    });

    it('shows configuration required alert when provider is not configured', () => {
        const propsWithUnconfiguredProvider = {
            ...mockProps,
            provider_status: {
                provider: 'smtp',
                configured: false,
                status: 'not_configured',
            },
        };

        render(<EmailConfigIndex {...propsWithUnconfiguredProvider} />);

        expect(screen.getByText('Configuration Required:')).toBeInTheDocument();
        expect(screen.getByText('Your email provider is not properly configured. Please complete the configuration below to enable email functionality.')).toBeInTheDocument();
    });

    it('shows incomplete configuration alert when provider is partially configured', () => {
        const propsWithIncompleteProvider = {
            ...mockProps,
            provider_status: {
                provider: 'smtp',
                configured: true,
                status: 'incomplete',
            },
        };

        render(<EmailConfigIndex {...propsWithIncompleteProvider} />);

        expect(screen.getByText('Configuration Incomplete:')).toBeInTheDocument();
        expect(screen.getByText('Some required settings are missing. Please provide all required credentials to enable email functionality.')).toBeInTheDocument();
    });

    it('shows configured status alert when provider is configured', () => {
        const propsWithConfiguredProvider = {
            ...mockProps,
            provider_status: {
                provider: 'smtp',
                configured: true,
                status: 'configured',
            },
        };

        render(<EmailConfigIndex {...propsWithConfiguredProvider} />);

        expect(screen.getByText('Configuration Complete:')).toBeInTheDocument();
        expect(screen.getByText('Email provider is configured. Use "Test Config" to verify connectivity, or send a test email to confirm functionality.')).toBeInTheDocument();
    });

    it('displays troubleshooting information when error_details are provided', () => {
        const propsWithTroubleshooting = {
            ...mockProps,
            error_details: {
                message: 'SMTP connection failed',
                troubleshooting: [
                    'Gmail SMTP Configuration:',
                    '• Enable 2-Factor Authentication on your Gmail account',
                    '• Generate an App Password: https://myaccount.google.com/apppasswords',
                    '',
                    'Connection Timeout Issues:',
                    '• Check your internet connection',
                    '• Verify firewall settings allow SMTP connections'
                ]
            }
        };

        render(<EmailConfigIndex {...propsWithTroubleshooting} />);

        expect(screen.getByText('Troubleshooting Guide:')).toBeInTheDocument();
        expect(screen.getByText('Gmail SMTP Configuration:')).toBeInTheDocument();
        expect(screen.getByText('• Enable 2-Factor Authentication on your Gmail account')).toBeInTheDocument();
        expect(screen.getByText('Connection Timeout Issues:')).toBeInTheDocument();
        expect(screen.getByText('• Check your internet connection')).toBeInTheDocument();
    });

    it('shows correct status badges for different provider states', () => {
        const testCases = [
            { status: 'healthy', expectedText: 'Healthy' },
            { status: 'configured', expectedText: 'Configured' },
            { status: 'incomplete', expectedText: 'Incomplete' },
            { status: 'not_configured', expectedText: 'Not Configured' },
            { status: 'unhealthy', expectedText: 'Connection Failed' },
            { status: 'unknown', expectedText: 'Unknown' },
        ];

        testCases.forEach(({ status, expectedText }) => {
            const propsWithStatus = {
                ...mockProps,
                provider_status: {
                    ...mockProps.provider_status,
                    status,
                },
            };

            const { unmount } = render(<EmailConfigIndex {...propsWithStatus} />);
            expect(screen.getByText(expectedText)).toBeInTheDocument();
            unmount();
        });
    });

    it('displays zero stats when no emails have been sent', () => {
        const propsWithZeroStats = {
            ...mockProps,
            email_stats: {
                ...mockProps.email_stats,
                total_sent: 0,
                total_delivered: 0,
                total_bounced: 0,
                total_opened: 0,
                total_clicked: 0,
                delivery_rate: 0,
                open_rate: 0,
                click_rate: 0,
                bounce_rate: 0,
            },
        };
        
        render(<EmailConfigIndex {...propsWithZeroStats} />);
        
        expect(screen.getAllByText('0.0%')).toHaveLength(2); // Should show multiple 0.0% values
    });
});
