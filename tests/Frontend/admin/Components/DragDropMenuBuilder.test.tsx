import { render, screen, waitFor } from '../../../utils/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { DragDropMenuBuilder } from '@/components/admin/DragDropMenuBuilder';
import { MenuItem } from '@/utils/menuTreeUtils';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
    router: {
        reload: vi.fn(),
    },
}));

vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

vi.mock('dnd-kit-sortable-tree', () => ({
    SortableTree: ({ items, onItemsChanged, TreeItemComponent }: any) => {
        // Recursively render tree items including children
        const renderTreeItems = (treeItems: any[]): any[] => {
            if (!treeItems || treeItems.length === 0) return [];

            return treeItems.flatMap((item: any) => [
                <div key={item.id} data-testid={`tree-item-${item.id}`}>
                    {TreeItemComponent && <TreeItemComponent item={item} />}
                </div>,
                ...(item.children ? renderTreeItems(item.children) : [])
            ]);
        };

        return (
            <div data-testid="sortable-tree">
                {renderTreeItems(items || [])}
            </div>
        );
    },
}));

vi.mock('@/components/admin/MenuTreeItem', () => ({
    MenuTreeItem: vi.fn(({ item }) => (
        <div data-testid={`menu-tree-item-${item.id}`}>
            {item.title}
        </div>
    )),
}));

describe('DragDropMenuBuilder Component', () => {
    const mockItemTypes = {
        custom: 'Custom Link',
        page: 'Page',
        category: 'Category',
    };

    const mockOnEditItem = vi.fn();
    const mockOnAddItem = vi.fn();

    const createMockMenuItem = (id: number, title: string, parentId: number | null = null): MenuItem => ({
        id,
        menu_id: 1,
        parent_id: parentId,
        title,
        url: null,
        target: '_self',
        icon: null,
        css_class: null,
        type: 'custom',
        reference_id: null,
        order: id,
        is_active: true,
    });

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders menu builder with initial items', () => {
        const items = [
            createMockMenuItem(1, 'Home'),
            createMockMenuItem(2, 'About'),
        ];

        render(
            <DragDropMenuBuilder
                menuId={1}
                items={items}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        expect(screen.getByText('Menu Structure')).toBeInTheDocument();
        expect(screen.getByTestId('sortable-tree')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-1')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-2')).toBeInTheDocument();
    });

    it('updates tree items when items prop changes', async () => {
        const initialItems = [
            createMockMenuItem(1, 'Home'),
        ];

        const { rerender } = render(
            <DragDropMenuBuilder
                menuId={1}
                items={initialItems}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        // Initial render should show one item
        expect(screen.getByTestId('tree-item-1')).toBeInTheDocument();

        // Update props with new items (simulating data reload after edit)
        const updatedItems = [
            createMockMenuItem(1, 'Home'),
            createMockMenuItem(2, 'About'),
            createMockMenuItem(3, 'Contact', 2), // Child of About
        ];

        rerender(
            <DragDropMenuBuilder
                menuId={1}
                items={updatedItems}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        // Should now show all three items
        await waitFor(() => {
            expect(screen.getByTestId('tree-item-1')).toBeInTheDocument();
            expect(screen.getByTestId('tree-item-2')).toBeInTheDocument();
            expect(screen.getByTestId('tree-item-3')).toBeInTheDocument();
        });
    });

    it('handles hierarchical menu structure correctly', () => {
        const items = [
            createMockMenuItem(1, 'Parent'),
            createMockMenuItem(2, 'Child 1', 1),
            createMockMenuItem(3, 'Child 2', 1),
            createMockMenuItem(4, 'Grandchild', 2),
        ];

        render(
            <DragDropMenuBuilder
                menuId={1}
                items={items}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        // All items should be rendered (flatToTree should handle the hierarchy)
        expect(screen.getByTestId('tree-item-1')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-2')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-3')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-4')).toBeInTheDocument();
    });

    it('handles empty items array', () => {
        render(
            <DragDropMenuBuilder
                menuId={1}
                items={[]}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        expect(screen.getByText('Menu Structure')).toBeInTheDocument();
        expect(screen.getByText('No Menu Items')).toBeInTheDocument();
        expect(screen.getByText('Add First Menu Item')).toBeInTheDocument();
    });

    it('preserves inactive items in admin interface', async () => {
        const items = [
            { ...createMockMenuItem(1, 'Active Item'), is_active: true },
            { ...createMockMenuItem(2, 'Inactive Item'), is_active: false },
            { ...createMockMenuItem(3, 'Child of Inactive', 2), is_active: true },
        ];

        render(
            <DragDropMenuBuilder
                menuId={1}
                items={items}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        // All items should be visible in admin interface, regardless of active status
        expect(screen.getByTestId('tree-item-1')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-2')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-3')).toBeInTheDocument();
    });

    it('handles rapid prop updates without breaking', async () => {
        const initialItems = [createMockMenuItem(1, 'Item 1')];

        const { rerender } = render(
            <DragDropMenuBuilder
                menuId={1}
                items={initialItems}
                itemTypes={mockItemTypes}
                onEditItem={mockOnEditItem}
                onAddItem={mockOnAddItem}
            />
        );

        // Simulate rapid updates (like what might happen during editing)
        const updates = [
            [createMockMenuItem(1, 'Item 1'), createMockMenuItem(2, 'Item 2')],
            [createMockMenuItem(1, 'Item 1'), createMockMenuItem(2, 'Item 2'), createMockMenuItem(3, 'Item 3')],
            [createMockMenuItem(1, 'Updated Item 1'), createMockMenuItem(2, 'Item 2'), createMockMenuItem(3, 'Item 3')],
        ];

        for (const items of updates) {
            rerender(
                <DragDropMenuBuilder
                    menuId={1}
                    items={items}
                    itemTypes={mockItemTypes}
                    onEditItem={mockOnEditItem}
                    onAddItem={mockOnAddItem}
                />
            );

            await waitFor(() => {
                items.forEach(item => {
                    expect(screen.getByTestId(`tree-item-${item.id}`)).toBeInTheDocument();
                });
            });
        }
    });
});
