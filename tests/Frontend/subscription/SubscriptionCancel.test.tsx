import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { router } from '@inertiajs/react';
import Dashboard from '@/pages/subscription/dashboard';
import Cancelled from '@/pages/subscription/Cancelled';
import PaddleCancelled from '@/pages/subscription/paddle/Cancelled';

// Mock Inertia router and usePage
vi.mock('@inertiajs/react', async () => {
    const actual = await vi.importActual('@inertiajs/react');
    return {
        ...actual,
        router: {
            post: vi.fn(),
            get: vi.fn(),
        },
        route: vi.fn((name: string, params?: any) => {
            const routes: Record<string, string> = {
                'subscription.cancel': '/subscription/cancel',
                'subscription.cancelled': '/subscription/cancelled',
                'subscription.plans': '/subscription/plans',
                'dashboard': '/dashboard',
                'payment-requests.create': '/payment-requests/create',
            };
            return routes[name] || `/${name}`;
        }),
        usePage: vi.fn(() => ({
            props: {
                auth: {
                    user: {
                        id: 1,
                        name: 'Test User',
                        email: '<EMAIL>',
                        isAdmin: false,
                    },
                },
            },
            url: '/subscription/dashboard',
            component: 'subscription/dashboard',
        })),
        Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
        Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    };
});

// Mock window.confirm
Object.defineProperty(window, 'confirm', {
    writable: true,
    value: vi.fn(),
});

// Mock toast notifications
vi.mock('react-hot-toast', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock global route function
global.route = vi.fn((name: string, params?: any) => {
    const routes: Record<string, string> = {
        'subscription.cancel': '/subscription/cancel',
        'subscription.cancelled': '/subscription/cancelled',
        'subscription.plans': '/subscription/plans',
        'dashboard': '/dashboard',
        'payment-requests.create': '/payment-requests/create',
        'payment-history.index': '/payment-history',
    };
    return routes[name] || `/${name}`;
});

describe('Subscription Cancel Functionality', () => {
    const mockUser = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        subscription_plan: 'premium',
    };

    const mockSubscription = {
        id: 1,
        status: 'active',
        current_period_end: '2024-12-31T23:59:59.000000Z',
        plan_name: 'premium',
    };

    const mockSearchHistory = [
        {
            id: 1,
            query: 'iPhone 12',
            results_count: 5,
            created_at: '2024-01-01T00:00:00.000000Z',
        },
    ];

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Dashboard Cancel Button', () => {
        it('renders cancel subscription button for premium users', () => {
            render(
                <Dashboard
                    subscription={mockSubscription}
                    currentPlan="premium"
                    remainingSearches={100}
                    searchHistory={mockSearchHistory}
                />
            );

            const cancelButton = screen.getByText(/cancel subscription/i);
            expect(cancelButton).toBeInTheDocument();
        });

        it('shows confirmation dialog when cancel button is clicked', () => {
            const mockConfirm = vi.fn().mockReturnValue(true);
            window.confirm = mockConfirm;

            render(
                <Dashboard
                    subscription={mockSubscription}
                    currentPlan="premium"
                    remainingSearches={100}
                    searchHistory={mockSearchHistory}
                />
            );

            const cancelButton = screen.getByText(/cancel subscription/i);
            fireEvent.click(cancelButton);

            expect(mockConfirm).toHaveBeenCalledWith(
                'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.'
            );
        });

        it('calls router.post when user confirms cancellation', () => {
            const mockConfirm = vi.fn().mockReturnValue(true);
            window.confirm = mockConfirm;

            render(
                <Dashboard
                    subscription={mockSubscription}
                    currentPlan="premium"
                    remainingSearches={100}
                    searchHistory={mockSearchHistory}
                />
            );

            const cancelButton = screen.getByText(/cancel subscription/i);
            fireEvent.click(cancelButton);

            expect(router.post).toHaveBeenCalledWith('/subscription/cancel');
        });

        it('does not call router.post when user cancels confirmation', () => {
            const mockConfirm = vi.fn().mockReturnValue(false);
            window.confirm = mockConfirm;

            render(
                <Dashboard
                    subscription={mockSubscription}
                    currentPlan="premium"
                    remainingSearches={100}
                    searchHistory={mockSearchHistory}
                />
            );

            const cancelButton = screen.getByText(/cancel subscription/i);
            fireEvent.click(cancelButton);

            expect(router.post).not.toHaveBeenCalled();
        });
    });

    describe('Cancelled Page', () => {
        const mockPlans = {
            premium: {
                id: 1,
                name: 'Premium',
                price: 19,
                features: ['Unlimited searches'],
            },
        };

        it('renders cancelled page with reason', () => {
            render(
                <Cancelled
                    user={mockUser}
                    reason="Payment was cancelled"
                    plans={mockPlans}
                    currentPlan="free"
                    remainingSearches={20}
                />
            );

            expect(screen.getByText(/payment cancelled/i)).toBeInTheDocument();
            expect(screen.getByText('Payment was cancelled')).toBeInTheDocument();
        });

        it('shows retry payment button', () => {
            render(
                <Cancelled
                    user={mockUser}
                    reason="Payment was cancelled"
                    plans={mockPlans}
                    currentPlan="free"
                    remainingSearches={20}
                />
            );

            const retryButton = screen.getByText(/try this plan/i);
            expect(retryButton).toBeInTheDocument();
        });

        it('shows view all plans button', () => {
            render(
                <Cancelled
                    user={mockUser}
                    reason="Payment was cancelled"
                    plans={mockPlans}
                    currentPlan="free"
                    remainingSearches={20}
                />
            );

            const plansButton = screen.getByText(/view all plans/i);
            expect(plansButton).toBeInTheDocument();
        });

        it('shows back to dashboard button', () => {
            render(
                <Cancelled
                    user={mockUser}
                    reason="Payment was cancelled"
                    plans={mockPlans}
                    currentPlan="free"
                    remainingSearches={20}
                />
            );

            const dashboardButton = screen.getByText(/back to dashboard/i);
            expect(dashboardButton).toBeInTheDocument();
        });
    });

    describe('Paddle Cancelled Page', () => {
        it('renders paddle cancelled page', () => {
            render(<PaddleCancelled />);

            expect(screen.getByText(/payment cancelled/i)).toBeInTheDocument();
        });

        it('shows try payment again button', () => {
            render(<PaddleCancelled />);

            const retryButton = screen.getByRole('button', { name: /try payment again/i });
            expect(retryButton).toBeInTheDocument();
        });

        it('shows submit offline payment button', () => {
            render(<PaddleCancelled />);

            const offlineButton = screen.getByRole('button', { name: /submit offline payment/i });
            expect(offlineButton).toBeInTheDocument();
        });

        it('shows return to dashboard button', () => {
            render(<PaddleCancelled />);

            const dashboardButton = screen.getByRole('button', { name: /return to dashboard/i });
            expect(dashboardButton).toBeInTheDocument();
        });
    });

    describe('Error Handling', () => {
        it('handles missing subscription gracefully', () => {
            render(
                <Dashboard
                    subscription={null}
                    currentPlan="free"
                    remainingSearches={20}
                    searchHistory={mockSearchHistory}
                />
            );

            // Should not show cancel button for users without subscription
            const cancelButton = screen.queryByText(/cancel subscription/i);
            expect(cancelButton).not.toBeInTheDocument();
        });

        it('handles cancelled subscription status', () => {
            const cancelledSubscription = {
                ...mockSubscription,
                status: 'cancelled',
            };

            render(
                <Dashboard
                    subscription={cancelledSubscription}
                    currentPlan="free"
                    remainingSearches={20}
                    searchHistory={mockSearchHistory}
                />
            );

            // Should show appropriate status for cancelled subscription
            // There are multiple "cancelled" texts, so we'll check that at least one exists
            expect(screen.getAllByText('cancelled')).toHaveLength(2);
        });
    });

    describe('Accessibility', () => {
        it('has proper ARIA labels for cancel button', () => {
            render(
                <Dashboard
                    subscription={mockSubscription}
                    currentPlan="premium"
                    remainingSearches={100}
                    searchHistory={mockSearchHistory}
                />
            );

            const cancelButton = screen.getByText(/cancel subscription/i);
            expect(cancelButton).toBeInTheDocument();
            // The button should be accessible
            expect(cancelButton.tagName).toBe('BUTTON');
        });

        it('has proper heading structure on cancelled page', () => {
            render(
                <Cancelled
                    user={mockUser}
                    reason="Payment was cancelled"
                    plans={{}}
                    currentPlan="free"
                    remainingSearches={20}
                />
            );

            const heading = screen.getByRole('heading', { level: 1 });
            expect(heading).toBeInTheDocument();
        });
    });
});
