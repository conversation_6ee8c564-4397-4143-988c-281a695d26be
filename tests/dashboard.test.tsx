import { render, screen } from './utils/test-utils';
import { describe, it, expect, vi } from 'vitest';
import Dashboard from '@/pages/dashboard';
import { usePage } from '@inertiajs/react';
import type { DashboardData, User } from '@/types';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    usePage: vi.fn(),
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href}>{children}</a>
    ),
}));

// Mock the route helper
vi.mock('@/utils/route', () => ({
    route: (name: string) => `/${name.replace('.', '/')}`,
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

const mockUser: User = {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    subscription_plan: 'free',
    subscription_status: 'active',
    email_verified_at: '2024-01-01T00:00:00.000000Z',
    created_at: '2024-01-01T00:00:00.000000Z',
    updated_at: '2024-01-01T00:00:00.000000Z',
};

const mockDashboardData: DashboardData = {
    stats: {
        total_searches: 156,
        searches_today: 12,
        searches_this_week: 45,
        week_growth_percentage: 15.5,
        success_rate: 89,
        favorite_items: 23,
        remaining_searches: 8,
        is_premium: false,
        subscription_plan: 'free',
    },
    recent_searches: [
        {
            id: 1,
            query: 'iPhone 15 Pro Max Display',
            type: 'part',
            results: 15,
            date: '2 hours ago',
            created_at: '2024-01-01T10:00:00.000000Z',
        },
        {
            id: 2,
            query: 'Samsung Galaxy S24',
            type: 'model',
            results: 8,
            date: '5 hours ago',
            created_at: '2024-01-01T07:00:00.000000Z',
        },
    ],
    top_categories: [
        { name: 'Display', count: 45, percentage: 28 },
        { name: 'Battery', count: 38, percentage: 24 },
        { name: 'Camera', count: 32, percentage: 20 },
    ],
    notifications_count: 5,
    subscription_info: {
        plan: 'free',
        status: 'active',
        is_premium: false,
        ends_at: null,
        active_subscription: null,
    },
    search_analytics: {
        daily_activity: [
            {
                date: '2024-01-01',
                searches: 10,
                avg_results: 15.5,
                success_rate: 85,
            },
        ],
    },
};

const mockPageProps = {
    auth: { user: mockUser },
    ...mockDashboardData,
};

describe('Dashboard Component', () => {
    beforeEach(() => {
        (usePage as any).mockReturnValue({
            props: mockPageProps,
        });
    });

    it('renders dashboard with user name', () => {
        render(<Dashboard />);
        expect(screen.getByText('Welcome back, John Doe!')).toBeInTheDocument();
    });

    it('displays correct statistics', () => {
        render(<Dashboard />);

        // Check main stats - use getAllByText since values appear multiple times
        expect(screen.getAllByText('156')).toHaveLength(2); // Total searches appears twice
        expect(screen.getAllByText('12')).toHaveLength(2); // Searches today appears twice
        expect(screen.getAllByText('89%')).toHaveLength(2); // Success rate appears twice
        expect(screen.getByText('Free')).toBeInTheDocument(); // Plan type
    });

    it('shows remaining searches for free users', () => {
        render(<Dashboard />);
        expect(screen.getByText('8 remaining today')).toBeInTheDocument();
    });

    it('displays recent search activity', () => {
        render(<Dashboard />);
        
        expect(screen.getByText('iPhone 15 Pro Max Display')).toBeInTheDocument();
        expect(screen.getByText('Samsung Galaxy S24')).toBeInTheDocument();
        expect(screen.getByText('15 results')).toBeInTheDocument();
        expect(screen.getByText('8 results')).toBeInTheDocument();
    });

    it('shows top search categories', () => {
        render(<Dashboard />);
        
        expect(screen.getByText('Display')).toBeInTheDocument();
        expect(screen.getByText('Battery')).toBeInTheDocument();
        expect(screen.getByText('Camera')).toBeInTheDocument();
        expect(screen.getByText('45 searches')).toBeInTheDocument();
        expect(screen.getByText('28%')).toBeInTheDocument();
    });

    it('displays correct notification count', () => {
        render(<Dashboard />);
        expect(screen.getByText('5 new updates')).toBeInTheDocument();
    });

    it('shows upgrade recommendation for free users', () => {
        render(<Dashboard />);
        expect(screen.getByText('Upgrade Recommendation')).toBeInTheDocument();
        expect(screen.getByText('Get unlimited searches and advanced features with Premium.')).toBeInTheDocument();
    });

    it('displays week growth percentage correctly', () => {
        render(<Dashboard />);
        expect(screen.getByText('+15.5% from last week')).toBeInTheDocument();
    });

    it('handles premium user correctly', () => {
        const premiumUser = { ...mockUser, subscription_plan: 'premium' };
        const premiumStats = { ...mockDashboardData.stats, is_premium: true, subscription_plan: 'premium' };
        const premiumProps = {
            ...mockPageProps,
            auth: { user: premiumUser },
            stats: premiumStats,
        };

        (usePage as any).mockReturnValue({
            props: premiumProps,
        });

        render(<Dashboard />);

        // Use getAllByText and check that at least one Premium element exists
        expect(screen.getAllByText('Premium')).toHaveLength(2);
        expect(screen.getByText('Unlimited remaining')).toBeInTheDocument();
        expect(screen.queryByText('Upgrade Recommendation')).not.toBeInTheDocument();
    });

    it('handles empty recent searches', () => {
        const emptySearchProps = {
            ...mockPageProps,
            recent_searches: [],
        };

        (usePage as any).mockReturnValue({
            props: emptySearchProps,
        });

        render(<Dashboard />);
        
        expect(screen.getByText('No searches yet')).toBeInTheDocument();
        expect(screen.getByText('Start exploring our mobile parts database to see your search history here')).toBeInTheDocument();
    });

    it('handles empty top categories', () => {
        const emptyCategoriesProps = {
            ...mockPageProps,
            top_categories: [],
        };

        (usePage as any).mockReturnValue({
            props: emptyCategoriesProps,
        });

        render(<Dashboard />);
        
        expect(screen.getByText('No search data available yet. Start searching to see your top categories!')).toBeInTheDocument();
    });

    it('displays correct favorite items count', () => {
        render(<Dashboard />);
        expect(screen.getByText('23 saved items')).toBeInTheDocument();
    });

    it('shows correct search history count', () => {
        render(<Dashboard />);
        expect(screen.getByText('156 total searches')).toBeInTheDocument();
    });

    it('handles negative growth percentage', () => {
        const negativeGrowthStats = { ...mockDashboardData.stats, week_growth_percentage: -10.5 };
        const negativeGrowthProps = {
            ...mockPageProps,
            stats: negativeGrowthStats,
        };

        (usePage as any).mockReturnValue({
            props: negativeGrowthProps,
        });

        render(<Dashboard />);
        
        expect(screen.getByText('-10.5% from last week')).toBeInTheDocument();
    });

    it('renders all quick action buttons', () => {
        render(<Dashboard />);

        expect(screen.getByText('Search Parts')).toBeInTheDocument();
        // Use getAllByText and check that at least one Favorites element exists
        expect(screen.getAllByText('Favorites')).toHaveLength(2);
        expect(screen.getByText('Search History')).toBeInTheDocument();
        expect(screen.getByText('Usage Analytics')).toBeInTheDocument();
        expect(screen.getByText('Notifications')).toBeInTheDocument();
        expect(screen.getByText('Activity Log')).toBeInTheDocument();
    });

    it('displays account status correctly', () => {
        render(<Dashboard />);
        
        expect(screen.getByText('Account Status')).toBeInTheDocument();
        expect(screen.getByText('Free Plan')).toBeInTheDocument();
        expect(screen.getByText('Active')).toBeInTheDocument();
    });

    it('shows usage summary with correct values', () => {
        render(<Dashboard />);

        // Check usage summary section
        const usageSummary = screen.getByText('Usage Summary').closest('div');
        expect(usageSummary).toBeInTheDocument();

        // Just check that the values exist in the document
        expect(screen.getByText('Usage Summary')).toBeInTheDocument();
    });
});
