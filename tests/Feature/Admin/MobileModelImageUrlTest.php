<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MobileModelImageUrlTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected Brand $brand;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
        ]);
        
        // Create a brand for testing
        $this->brand = Brand::factory()->create([
            'name' => 'Test Brand',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function admin_can_create_model_with_image_url()
    {
        $this->actingAs($this->adminUser);

        $modelData = [
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'model_number' => 'TM001',
            'release_year' => 2024,
            'image_url' => 'https://example.com/test-image.jpg',
            'is_active' => true,
        ];

        $response = $this->post(route('admin.models.store'), $modelData);

        $response->assertRedirect(route('admin.models.index'));
        $response->assertSessionHas('success', 'Model created successfully.');

        $this->assertDatabaseHas('models', [
            'name' => 'Test Model',
            'image_url' => 'https://example.com/test-image.jpg',
        ]);
    }

    /** @test */
    public function admin_can_update_model_with_image_url()
    {
        $this->actingAs($this->adminUser);

        $model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Original Model',
            'image_url' => null,
        ]);

        $updateData = [
            'brand_id' => $this->brand->id,
            'name' => 'Updated Model',
            'image_url' => 'https://example.com/updated-image.jpg',
            'is_active' => true,
        ];

        $response = $this->put(route('admin.models.update', $model), $updateData);

        $response->assertRedirect(route('admin.models.index'));
        $response->assertSessionHas('success', 'Model updated successfully.');

        $this->assertDatabaseHas('models', [
            'id' => $model->id,
            'name' => 'Updated Model',
            'image_url' => 'https://example.com/updated-image.jpg',
        ]);
    }

    /** @test */
    public function image_url_validation_works_correctly()
    {
        $this->actingAs($this->adminUser);

        // Test invalid URL
        $invalidData = [
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'image_url' => 'not-a-valid-url',
            'is_active' => true,
        ];

        $response = $this->post(route('admin.models.store'), $invalidData);
        $response->assertSessionHasErrors(['image_url']);

        // Test URL too long
        $longUrlData = [
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'image_url' => 'https://example.com/' . str_repeat('a', 2050),
            'is_active' => true,
        ];

        $response = $this->post(route('admin.models.store'), $longUrlData);
        $response->assertSessionHasErrors(['image_url']);
    }

    /** @test */
    public function export_includes_image_url_column()
    {
        $this->actingAs($this->adminUser);

        // Create models with and without image URLs
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model With Image',
            'image_url' => 'https://example.com/image1.jpg',
        ]);

        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model Without Image',
            'image_url' => null,
        ]);

        $response = $this->get(route('admin.models.export'));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // For streamed responses, we need to capture the output
        ob_start();
        $response->sendContent();
        $content = ob_get_clean();

        // Check that the header includes Image URL
        $this->assertStringContainsString('Image URL', $content);

        // Check that the data includes the image URL
        $this->assertStringContainsString('https://example.com/image1.jpg', $content);

        // Check that empty image URLs are handled correctly
        $lines = explode("\n", $content);
        $this->assertGreaterThan(2, count($lines)); // Header + at least 2 data rows
    }

    /** @test */
    public function import_handles_image_url_column()
    {
        Storage::fake('local');
        $this->actingAs($this->adminUser);

        // Create CSV content with image URL column
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Image URL,Status\n";
        $csvContent .= "Test Brand,Import Model 1,IM001,2024,display: 6.1 inch,https://example.com/import1.jpg,Active\n";
        $csvContent .= "Test Brand,Import Model 2,IM002,2024,,https://example.com/import2.jpg,Active\n";
        $csvContent .= "Test Brand,Import Model 3,IM003,2024,,,Active\n"; // No image URL

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post(route('admin.bulk-import.models'), [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify models were created with correct image URLs
        $this->assertDatabaseHas('models', [
            'name' => 'Import Model 1',
            'image_url' => 'https://example.com/import1.jpg',
        ]);

        $this->assertDatabaseHas('models', [
            'name' => 'Import Model 2',
            'image_url' => 'https://example.com/import2.jpg',
        ]);

        $this->assertDatabaseHas('models', [
            'name' => 'Import Model 3',
            'image_url' => null,
        ]);
    }

    /** @test */
    public function import_validates_image_url_format()
    {
        Storage::fake('local');
        $this->actingAs($this->adminUser);

        // Create CSV content with invalid image URL
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Image URL,Status\n";
        $csvContent .= "Test Brand,Import Model,IM001,2024,,invalid-url,Active\n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post(route('admin.bulk-import.models'), [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('import_errors');

        // Verify model was not created due to validation error
        $this->assertDatabaseMissing('models', [
            'name' => 'Import Model',
        ]);
    }

    /** @test */
    public function template_download_includes_image_url_column()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.models.download-template'));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // For streamed responses, we need to capture the output
        ob_start();
        $response->sendContent();
        $content = ob_get_clean();

        // Check that the template includes Image URL column
        $this->assertStringContainsString('Image URL', $content);

        // Check that the template includes example image URLs
        $this->assertStringContainsString('https://example.com/', $content);

        // Check that the template includes instructions for image URL
        $this->assertStringContainsString('Image URL: Optional - URL to the primary image', $content);
    }
}
