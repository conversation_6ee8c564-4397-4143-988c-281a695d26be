<?php

namespace Tests\Feature\Admin;

use Tests\TestCase;
use App\Models\User;
use App\Services\GoogleAnalyticsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\File;
use Mockery;

class GoogleAnalyticsConfigTest extends TestCase
{
    use RefreshDatabase;

    private $testEnvFile;
    private $originalEnvContent;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test .env file
        $this->testEnvFile = storage_path('testing/.env.analytics.test');
        $this->ensureDirectoryExists(dirname($this->testEnvFile));
        
        // Save original .env content for restoration
        $this->originalEnvContent = file_get_contents(base_path('.env'));
        
        // Create initial test .env content
        $initialContent = "APP_NAME=TestApp\nAPP_ENV=testing\n";
        file_put_contents($this->testEnvFile, $initialContent);
    }

    protected function tearDown(): void
    {
        // Clean up test file
        if (file_exists($this->testEnvFile)) {
            unlink($this->testEnvFile);
        }
        
        parent::tearDown();
    }

    private function ensureDirectoryExists($directory)
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    protected function createAdminUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'role' => 'admin',
            'email_verified_at' => now(),
        ], $attributes));
    }

    protected function createContentManagerUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'role' => 'content_manager',
            'email_verified_at' => now(),
        ], $attributes));
    }

    protected function createRegularUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'role' => 'user',
            'email_verified_at' => now(),
        ], $attributes));
    }

    /** @test */
    public function admin_can_access_google_analytics_config_page()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)->get('/admin/google-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/GoogleAnalytics/Index')
                ->has('config')
                ->has('status')
                ->has('validation')
        );
    }

    /** @test */
    public function content_manager_cannot_access_google_analytics_config_page()
    {
        $contentManager = $this->createContentManagerUser();

        $response = $this->actingAs($contentManager)->get('/admin/google-analytics');

        $response->assertStatus(403);
    }

    /** @test */
    public function regular_user_cannot_access_google_analytics_config_page()
    {
        $user = $this->createRegularUser();

        $response = $this->actingAs($user)->get('/admin/google-analytics');

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_google_analytics_config_page()
    {
        $response = $this->get('/admin/google-analytics');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function admin_can_update_google_analytics_configuration()
    {
        $admin = $this->createAdminUser();

        $configData = [
            'enabled' => true,
            'measurement_id' => 'G-1234567890',
            'debug' => false,
            'cookie_domain' => 'example.com',
            'cookie_expires' => 31536000,
            'anonymize_ip' => true,
            'consent_mode' => true,
            'cookie_consent_required' => true,
            'respect_dnt' => true,
            'data_retention_days' => 365,
        ];

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Google Analytics configuration updated successfully.');
    }

    /** @test */
    public function google_analytics_config_validates_measurement_id_format()
    {
        $admin = $this->createAdminUser();

        $configData = [
            'enabled' => true,
            'measurement_id' => 'invalid-format',
            'debug' => false,
        ];

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertSessionHasErrors(['measurement_id']);
    }

    /** @test */
    public function google_analytics_config_requires_measurement_id_when_enabled()
    {
        $admin = $this->createAdminUser();

        $configData = [
            'enabled' => true,
            'measurement_id' => '',
            'debug' => false,
        ];

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertSessionHasErrors(['measurement_id']);
    }

    /** @test */
    public function google_analytics_config_allows_empty_measurement_id_when_disabled()
    {
        $admin = $this->createAdminUser();

        $configData = [
            'enabled' => false,
            'measurement_id' => '',
            'debug' => false,
        ];

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function google_analytics_config_validates_data_retention_days()
    {
        $admin = $this->createAdminUser();

        // Test minimum value
        $configData = [
            'enabled' => true,
            'measurement_id' => 'G-1234567890',
            'data_retention_days' => 0,
        ];

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertSessionHasErrors(['data_retention_days']);

        // Test maximum value
        $configData['data_retention_days'] = 1500;

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertSessionHasErrors(['data_retention_days']);
    }

    /** @test */
    public function admin_can_test_google_analytics_configuration()
    {
        $admin = $this->createAdminUser();

        // Mock the GoogleAnalyticsService
        $mockService = Mockery::mock(GoogleAnalyticsService::class);
        $mockService->shouldReceive('testConfiguration')
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'Google Analytics configuration is valid and working correctly.'
            ]);

        $this->app->instance(GoogleAnalyticsService::class, $mockService);

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/test');

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Google Analytics configuration is valid and working correctly.');
    }

    /** @test */
    public function google_analytics_test_handles_configuration_errors()
    {
        $admin = $this->createAdminUser();

        // Mock the GoogleAnalyticsService to return an error
        $mockService = Mockery::mock(GoogleAnalyticsService::class);
        $mockService->shouldReceive('testConfiguration')
            ->once()
            ->andReturn([
                'success' => false,
                'message' => 'Invalid Measurement ID format.',
                'troubleshooting' => 'Please check your Measurement ID format.',
                'details' => 'The Measurement ID should be in the format G-XXXXXXXXXX'
            ]);

        $this->app->instance(GoogleAnalyticsService::class, $mockService);

        $response = $this->actingAs($admin)
            ->post('/admin/google-analytics/test');

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Invalid Measurement ID format.');
        $response->assertSessionHas('error_details');
    }

    /** @test */
    public function google_analytics_documentation_endpoint_returns_correct_data()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)
            ->get('/admin/google-analytics/docs');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'title',
            'description',
            'setup_steps',
            'features',
            'privacy_compliance'
        ]);

        $data = $response->json();
        $this->assertEquals('Google Analytics 4 Configuration', $data['title']);
        $this->assertIsArray($data['setup_steps']);
        $this->assertIsArray($data['features']);
        $this->assertIsArray($data['privacy_compliance']);
    }

    /** @test */
    public function google_analytics_config_page_handles_service_errors_gracefully()
    {
        $admin = $this->createAdminUser();

        // Mock the GoogleAnalyticsService to throw an exception
        $mockService = Mockery::mock(GoogleAnalyticsService::class);
        $mockService->shouldReceive('getStatus')
            ->once()
            ->andThrow(new \Exception('Service unavailable'));

        // The middleware also calls getConfig(), so we need to mock that too
        $mockService->shouldReceive('getConfig')
            ->andReturn([
                'enabled' => false,
            ]);

        $this->app->instance(GoogleAnalyticsService::class, $mockService);

        $response = $this->actingAs($admin)->get('/admin/google-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/GoogleAnalytics/Index')
                ->has('error_details')
                ->where('error_details.message', 'Failed to load Google Analytics configuration')
        );
    }

    /** @test */
    public function content_manager_cannot_update_google_analytics_config()
    {
        $contentManager = $this->createContentManagerUser();

        $configData = [
            'enabled' => true,
            'measurement_id' => 'G-1234567890',
            'debug' => false,
        ];

        $response = $this->actingAs($contentManager)
            ->post('/admin/google-analytics/config', $configData);

        $response->assertStatus(403);
    }

    /** @test */
    public function content_manager_cannot_test_google_analytics_config()
    {
        $contentManager = $this->createContentManagerUser();

        $response = $this->actingAs($contentManager)
            ->post('/admin/google-analytics/test');

        $response->assertStatus(403);
    }
}
