<?php

namespace Tests\Feature;

use App\Mail\UserWelcome;
use App\Mail\PasswordChanged;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NewEmailClassesTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_welcome_email_can_be_created_and_rendered()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $password = 'test123';
        $mail = new UserWelcome($user, $password);

        // Test envelope
        $envelope = $mail->envelope();
        $this->assertEquals('Welcome to Mobile Parts DB - Your Account is Ready', $envelope->subject);

        // Test content
        $content = $mail->content();
        $this->assertEquals('emails.user-welcome', $content->html);
        $this->assertEquals('emails.user-welcome-text', $content->text);

        // Test with data
        $with = $content->with;
        $this->assertEquals($user->id, $with['user']->id);
        $this->assertEquals($password, $with['password']);
        $this->assertArrayHasKey('loginUrl', $with);
        $this->assertArrayHasKey('dashboardUrl', $with);
        $this->assertArrayHasKey('supportUrl', $with);
    }

    /** @test */
    public function user_welcome_email_works_without_password()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $mail = new UserWelcome($user, null);

        // Test content
        $content = $mail->content();
        $with = $content->with;
        $this->assertEquals($user->id, $with['user']->id);
        $this->assertNull($with['password']);
    }

    /** @test */
    public function password_changed_email_can_be_created_and_rendered()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $newPassword = 'newpass123';
        $mail = new PasswordChanged($user, $newPassword);

        // Test envelope
        $envelope = $mail->envelope();
        $this->assertEquals('Password Changed - Mobile Parts DB', $envelope->subject);

        // Test content
        $content = $mail->content();
        $this->assertEquals('emails.password-changed', $content->html);
        $this->assertEquals('emails.password-changed-text', $content->text);

        // Test with data
        $with = $content->with;
        $this->assertEquals($user->id, $with['user']->id);
        $this->assertEquals($newPassword, $with['newPassword']);
        $this->assertArrayHasKey('loginUrl', $with);
        $this->assertArrayHasKey('dashboardUrl', $with);
        $this->assertArrayHasKey('supportUrl', $with);
        $this->assertArrayHasKey('changeTime', $with);
    }

    /** @test */
    public function password_changed_email_works_without_new_password()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $mail = new PasswordChanged($user, null);

        // Test content
        $content = $mail->content();
        $with = $content->with;
        $this->assertEquals($user->id, $with['user']->id);
        $this->assertNull($with['newPassword']);
    }

    /** @test */
    public function email_templates_exist_and_can_be_rendered()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        // Test UserWelcome template rendering
        $welcomeMail = new UserWelcome($user, 'test123');
        $welcomeContent = $welcomeMail->content();
        
        $view = view($welcomeContent->html, $welcomeContent->with);
        $htmlContent = $view->render();
        
        $this->assertStringContainsString('Test User', $htmlContent);
        $this->assertStringContainsString('Welcome to Mobile Parts DB', $htmlContent);
        $this->assertStringContainsString('test123', $htmlContent);

        // Test PasswordChanged template rendering
        $passwordMail = new PasswordChanged($user, 'newpass123');
        $passwordContent = $passwordMail->content();
        
        $view = view($passwordContent->html, $passwordContent->with);
        $htmlContent = $view->render();
        
        $this->assertStringContainsString('Test User', $htmlContent);
        $this->assertStringContainsString('Password Changed', $htmlContent);
        $this->assertStringContainsString('newpass123', $htmlContent);
    }
}
