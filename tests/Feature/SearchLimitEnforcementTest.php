<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\Part;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Tests\TestCase;

class SearchLimitEnforcementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $subscriptionService;
    protected $freeUser;
    protected $premiumUser;
    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = app(SubscriptionService::class);
        
        // Create pricing plans
        $this->createPricingPlans();
        
        // Create test users
        $this->createTestUsers();
        
        // Create test data for search functionality
        $this->createTestSearchData();
    }

    private function createPricingPlans(): void
    {
        PricingPlan::truncate();

        PricingPlan::factory()->create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'search_limit' => 20,
            'model_view_limit' => 5,
            'parts_per_model_limit' => 3,
            'is_active' => true,
            'is_default' => true,
        ]);

        PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'search_limit' => -1, // Unlimited
            'model_view_limit' => -1, // Unlimited
            'parts_per_model_limit' => 50,
            'is_active' => true,
        ]);
    }

    private function createTestUsers(): void
    {
        // Free user
        $this->freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 0,
            'daily_reset' => today(),
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        // Premium user with active subscription
        $this->premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'search_count' => 50,
            'daily_reset' => today(),
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        // Create premium subscription
        $premiumPlan = PricingPlan::where('name', 'premium')->first();
        Subscription::create([
            'user_id' => $this->premiumUser->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subDays(15),
            'current_period_end' => now()->addDays(15),
        ]);

        // Admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
            'search_count' => 100,
            'daily_reset' => today(),
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
    }

    private function createTestSearchData(): void
    {
        $category = Category::factory()->create(['name' => 'Test Category']);
        $brand = Brand::factory()->create(['name' => 'Test Brand']);
        $model = MobileModel::factory()->create([
            'name' => 'Test Model',
            'brand_id' => $brand->id,
        ]);
        
        Part::factory()->create([
            'name' => 'Test Part',
            'category_id' => $category->id,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function free_user_can_search_within_daily_limit()
    {
        $this->actingAs($this->freeUser);

        // User should be able to search (within limit)
        $this->assertTrue($this->freeUser->canSearch());
        $this->assertEquals(20, $this->freeUser->getRemainingSearches());

        // Perform a search
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(200);

        // Verify search count incremented
        $this->freeUser->refresh();
        $this->assertEquals(1, $this->freeUser->search_count);
        $this->assertEquals(19, $this->freeUser->getRemainingSearches());
    }

    /** @test */
    public function free_user_cannot_search_when_limit_exceeded()
    {
        // Set user at the limit
        $this->freeUser->update(['search_count' => 20]);
        $this->actingAs($this->freeUser);

        // User should not be able to search
        $this->assertFalse($this->freeUser->canSearch());
        $this->assertEquals(0, $this->freeUser->getRemainingSearches());

        // Attempt to search should be blocked
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(302); // Redirect to subscription plans
        $response->assertRedirect(route('subscription.plans'));
        $response->assertSessionHas('error', 'Daily search limit exceeded');
    }

    /** @test */
    public function free_user_gets_proper_error_message_when_limit_exceeded()
    {
        $this->freeUser->update(['search_count' => 20]);
        $this->actingAs($this->freeUser);

        // Test JSON response for API calls
        $response = $this->getJson('/search/results?q=test');
        $response->assertStatus(429);
        $response->assertJson([
            'error' => 'Daily search limit exceeded',
            'message' => 'You have reached your daily search limit. Upgrade to Premium for unlimited searches.',
            'remaining_searches' => 0,
            'upgrade_url' => route('subscription.plans'),
        ]);
    }

    /** @test */
    public function premium_user_has_unlimited_search_access()
    {
        $this->actingAs($this->premiumUser);

        // Premium user should have unlimited access
        $this->assertTrue($this->premiumUser->canSearch());
        $this->assertEquals(-1, $this->premiumUser->getRemainingSearches());

        // Should be able to search even with high search count
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(200);

        // Search count should NOT increment for premium users (they have unlimited searches)
        $this->premiumUser->refresh();
        $this->assertEquals(50, $this->premiumUser->search_count);
    }

    /** @test */
    public function admin_user_has_unlimited_search_access()
    {
        $this->actingAs($this->adminUser);

        // Admin user should have unlimited access
        $this->assertTrue($this->adminUser->canSearch());
        $this->assertEquals(-1, $this->adminUser->getRemainingSearches());

        // Should be able to search regardless of count
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(200);

        // Search count should NOT increment for admin users
        $this->adminUser->refresh();
        $this->assertEquals(100, $this->adminUser->search_count); // Should remain unchanged
    }

    /** @test */
    public function search_limits_reset_daily()
    {
        // Set user at limit with yesterday's reset date
        $this->freeUser->update([
            'search_count' => 20,
            'daily_reset' => now()->subDay(),
        ]);

        $this->actingAs($this->freeUser);

        // User should be able to search after daily reset
        $this->assertTrue($this->freeUser->canSearch());

        // Perform search to trigger reset
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(200);

        // Verify reset occurred
        $this->freeUser->refresh();
        $this->assertEquals(1, $this->freeUser->search_count); // Reset and incremented
        $this->assertEquals(today()->toDateString(), $this->freeUser->daily_reset->toDateString());
        $this->assertEquals(19, $this->freeUser->getRemainingSearches());
    }

    /** @test */
    public function search_limit_enforcement_works_across_sessions()
    {
        $this->actingAs($this->freeUser);

        // Perform multiple searches to approach limit
        for ($i = 0; $i < 19; $i++) {
            $response = $this->get('/search/results?q=test' . $i);
            $response->assertStatus(200);
        }

        // Verify we're at 19 searches
        $this->freeUser->refresh();
        $this->assertEquals(19, $this->freeUser->search_count);
        $this->assertEquals(1, $this->freeUser->getRemainingSearches());

        // One more search should work
        $response = $this->get('/search/results?q=final');
        $response->assertStatus(200);

        // Now we should be at the limit
        $this->freeUser->refresh();
        $this->assertEquals(20, $this->freeUser->search_count);
        $this->assertEquals(0, $this->freeUser->getRemainingSearches());

        // Next search should be blocked
        $response = $this->get('/search/results?q=blocked');
        $response->assertStatus(302);
        $response->assertRedirect(route('subscription.plans'));
    }

    /** @test */
    public function search_count_accuracy_is_maintained()
    {
        $this->actingAs($this->freeUser);

        // Perform exactly 10 searches
        for ($i = 0; $i < 10; $i++) {
            $response = $this->get('/search/results?q=test' . $i);
            $response->assertStatus(200);
        }

        // Verify exact count
        $this->freeUser->refresh();
        $this->assertEquals(10, $this->freeUser->search_count);
        $this->assertEquals(10, $this->freeUser->getRemainingSearches());

        // Verify subscription service agrees
        $this->assertEquals(10, $this->subscriptionService->getRemainingSearches($this->freeUser));
    }

    /** @test */
    public function middleware_blocks_search_requests_when_limit_exceeded()
    {
        $this->freeUser->update(['search_count' => 20]);
        $this->actingAs($this->freeUser);

        // Test that middleware blocks the request before it reaches the controller
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(302);
        $response->assertRedirect(route('subscription.plans'));

        // Test JSON requests get proper error response
        $response = $this->getJson('/search/results?q=test');
        $response->assertStatus(429);
        $response->assertJson(['error' => 'Daily search limit exceeded']);
    }

    /** @test */
    public function guest_users_are_redirected_to_login_by_auth_middleware()
    {
        // Test unauthenticated request - should be redirected to login by auth middleware
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(302);
        $response->assertRedirect(route('login'));

        // Test JSON request for unauthenticated user - should get 401 from auth middleware
        $response = $this->getJson('/search/results?q=test');
        $response->assertStatus(401);
    }
}
