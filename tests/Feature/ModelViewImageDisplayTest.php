<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Brand;
use App\Models\MobileModel;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ModelViewImageDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected User $regularUser;
    protected Brand $brand;
    protected MobileModel $modelWithImage;
    protected MobileModel $modelWithoutImage;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create users
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);
        
        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'email_verified_at' => now(),
        ]);
        
        // Create a brand for testing
        $this->brand = Brand::factory()->create([
            'name' => 'Test Brand',
            'is_active' => true,
        ]);

        // Create models with and without image URLs
        $this->modelWithImage = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model With Image',
            'model_number' => 'MWI001',
            'image_url' => 'https://example.com/model-image.jpg',
            'is_active' => true,
        ]);

        $this->modelWithoutImage = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model Without Image',
            'model_number' => 'MWO001',
            'image_url' => null,
            'is_active' => true,
        ]);

        // Refresh models to ensure slugs are generated
        $this->modelWithImage->refresh();
        $this->modelWithoutImage->refresh();
    }

    /** @test */
    public function admin_model_index_page_loads_successfully()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/models');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Models/Index')
                ->has('models.data')
        );
    }

    /** @test */
    public function admin_model_show_page_displays_image_url()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get("/admin/models/{$this->modelWithImage->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Show')
                ->where('model.image_url', 'https://example.com/model-image.jpg')
        );
    }

    /** @test */
    public function admin_model_show_page_handles_null_image_url()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get("/admin/models/{$this->modelWithoutImage->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Show')
                ->where('model.image_url', null)
        );
    }

    /** @test */
    public function public_model_view_displays_image_url()
    {
        $response = $this->get("/models/{$this->modelWithImage->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('public/model-view')
                ->where('model.image_url', 'https://example.com/model-image.jpg')
        );
    }

    /** @test */
    public function public_model_view_handles_null_image_url()
    {
        $response = $this->get("/models/{$this->modelWithoutImage->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('public/model-view')
                ->where('model.image_url', null)
        );
    }

    /** @test */
    public function user_model_view_displays_image_url()
    {
        $this->actingAs($this->regularUser);

        $response = $this->get("/models/{$this->modelWithImage->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('models/show')
                ->where('model.image_url', 'https://example.com/model-image.jpg')
        );
    }

    /** @test */
    public function user_model_view_handles_null_image_url()
    {
        $this->actingAs($this->regularUser);

        $response = $this->get("/models/{$this->modelWithoutImage->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('models/show')
                ->where('model.image_url', null)
        );
    }

    /** @test */
    public function admin_model_edit_page_displays_current_image_url()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get("/admin/models/{$this->modelWithImage->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Edit')
                ->where('model.image_url', 'https://example.com/model-image.jpg')
        );
    }

    /** @test */
    public function admin_model_create_page_has_image_url_field()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/models/create');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Create')
        );
    }





    /** @test */
    public function guest_can_view_model_with_image_url()
    {
        $response = $this->get("/models/{$this->modelWithImage->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('public/model-view')
                ->where('model.image_url', 'https://example.com/model-image.jpg')
        );
    }



    /** @test */
    public function model_image_url_can_be_updated_via_admin_edit()
    {
        $this->actingAs($this->adminUser);

        $updateData = [
            'brand_id' => $this->modelWithoutImage->brand_id,
            'name' => $this->modelWithoutImage->name,
            'model_number' => $this->modelWithoutImage->model_number,
            'image_url' => 'https://example.com/new-image.jpg',
            'is_active' => $this->modelWithoutImage->is_active,
        ];

        $response = $this->put("/admin/models/{$this->modelWithoutImage->id}", $updateData);

        $response->assertRedirect();
        
        $this->modelWithoutImage->refresh();
        $this->assertEquals('https://example.com/new-image.jpg', $this->modelWithoutImage->image_url);
    }

    /** @test */
    public function model_image_url_can_be_cleared_via_admin_edit()
    {
        $this->actingAs($this->adminUser);

        $updateData = [
            'brand_id' => $this->modelWithImage->brand_id,
            'name' => $this->modelWithImage->name,
            'model_number' => $this->modelWithImage->model_number,
            'image_url' => '', // Clear the image URL
            'is_active' => $this->modelWithImage->is_active,
        ];

        $response = $this->put("/admin/models/{$this->modelWithImage->id}", $updateData);

        $response->assertRedirect();
        
        $this->modelWithImage->refresh();
        $this->assertNull($this->modelWithImage->image_url);
    }
}
