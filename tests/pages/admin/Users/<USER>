import { render, screen } from '../../../utils/test-utils';
import { describe, it, expect, vi } from 'vitest';
import { createInertiaApp } from '@inertiajs/react';
import UsersIndex from '@/pages/admin/Users/<USER>';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <head>{children}</head>,
    Link: ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href}>{children}</a>
    ),
    router: {
        get: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        delete: vi.fn(),
    },
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock ImpersonationSecurityCheck
vi.mock('@/components/ImpersonationSecurityCheck', () => ({
    default: () => <div data-testid="impersonation-security-check" />,
}));

// Mock UserStatusDisplay
vi.mock('@/components/UserStatusDisplay', () => ({
    UserStatusDisplay: ({ status }: { status: string }) => <span data-testid="user-status">{status}</span>,
}));

describe('UsersIndex', () => {
    const mockStats = {
        total_users: 61,
        active_users: 60,
        pending_approval: 0,
        suspended_users: 0,
        premium_users: 13,
        email_verified: 60,
        email_unverified: 1,
        fully_active: 59,
    };

    const mockUsers = {
        data: [
            {
                id: 1,
                name: 'John Doe',
                email: '<EMAIL>',
                email_verified_at: '2023-01-01T00:00:00Z',
                status: 'active' as const,
                approval_status: 'approved' as const,
                subscription_plan: 'premium' as const,
                created_at: '2023-01-01T00:00:00Z',
                last_login_at: '2023-01-01T00:00:00Z',
                login_count: 5,
                searches_count: 10,
                payment_requests_count: 2,
                activity_logs_count: 15,
            },
        ],
        links: [],
        meta: {
            current_page: 1,
            from: 1,
            last_page: 1,
            per_page: 25,
            to: 1,
            total: 1,
        },
    };

    const mockFilters = {
        search: '',
        status: 'all',
        approval_status: 'all',
        subscription_plan: 'all',
    };

    const defaultProps = {
        users: mockUsers,
        stats: mockStats,
        filters: mockFilters,
    };

    it('renders the page title', () => {
        render(<UsersIndex {...defaultProps} />);
        
        expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    it('renders all stat cards with correct values', () => {
        render(<UsersIndex {...defaultProps} />);

        expect(screen.getByText('Total Users')).toBeInTheDocument();
        expect(screen.getAllByText('61')).toHaveLength(1);

        expect(screen.getByText('Active Users')).toBeInTheDocument();
        expect(screen.getAllByText('60').length).toBeGreaterThan(0);

        expect(screen.getByText('Pending Approval')).toBeInTheDocument();
        expect(screen.getAllByText('0').length).toBeGreaterThan(0);

        expect(screen.getByText('Suspended')).toBeInTheDocument();

        expect(screen.getByText('Premium Users')).toBeInTheDocument();
        expect(screen.getAllByText('13')).toHaveLength(1);

        expect(screen.getByText('Email Verified')).toBeInTheDocument();

        expect(screen.getByText('Email Unverified')).toBeInTheDocument();
        expect(screen.getAllByText('1').length).toBeGreaterThan(0);

        expect(screen.getByText('Fully Active')).toBeInTheDocument();
        expect(screen.getAllByText('59')).toHaveLength(1);
    });

    it('renders the stats grid with proper layout', () => {
        const { container } = render(<UsersIndex {...defaultProps} />);
        
        const statsGrid = container.querySelector('.grid.gap-4.sm\\:gap-6');
        expect(statsGrid).toBeInTheDocument();
        expect(statsGrid).toHaveClass('grid-cols-1', 'sm:grid-cols-2', 'xl:grid-cols-4');
    });

    it('renders Create User button', () => {
        render(<UsersIndex {...defaultProps} />);
        
        expect(screen.getByText('Create User')).toBeInTheDocument();
    });

    it('renders filters section', () => {
        render(<UsersIndex {...defaultProps} />);
        
        expect(screen.getByText('Filters')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Search users...')).toBeInTheDocument();
    });

    it('renders users table', () => {
        render(<UsersIndex {...defaultProps} />);
        
        expect(screen.getByText('Users')).toBeInTheDocument();
        expect(screen.getByText('1 users found')).toBeInTheDocument();
        
        // Check table headers
        expect(screen.getByText('Name')).toBeInTheDocument();
        expect(screen.getByText('Email')).toBeInTheDocument();
        expect(screen.getByText('Account Status')).toBeInTheDocument();
    });

    it('renders user data in table', () => {
        render(<UsersIndex {...defaultProps} />);
        
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    describe('Responsive Design', () => {
        it('has responsive grid classes', () => {
            const { container } = render(<UsersIndex {...defaultProps} />);
            
            const statsGrid = container.querySelector('.grid');
            expect(statsGrid).toHaveClass('grid-cols-1', 'sm:grid-cols-2', 'xl:grid-cols-4');
        });

        it('has responsive gap classes', () => {
            const { container } = render(<UsersIndex {...defaultProps} />);
            
            const statsGrid = container.querySelector('.grid');
            expect(statsGrid).toHaveClass('gap-4', 'sm:gap-6');
        });
    });

    describe('StatCard Integration', () => {
        it('renders all 8 stat cards', () => {
            render(<UsersIndex {...defaultProps} />);

            // Check for the specific stat card titles
            expect(screen.getByText('Total Users')).toBeInTheDocument();
            expect(screen.getByText('Active Users')).toBeInTheDocument();
            expect(screen.getByText('Pending Approval')).toBeInTheDocument();
            expect(screen.getByText('Suspended')).toBeInTheDocument();
            expect(screen.getByText('Premium Users')).toBeInTheDocument();
            expect(screen.getByText('Email Verified')).toBeInTheDocument();
            expect(screen.getByText('Email Unverified')).toBeInTheDocument();
            expect(screen.getByText('Fully Active')).toBeInTheDocument();
        });

        it('stat cards have proper hover effects', () => {
            const { container } = render(<UsersIndex {...defaultProps} />);

            // Look for the main stat card containers
            const statCards = container.querySelectorAll('[class*="hover:shadow-xl"]');
            expect(statCards.length).toBeGreaterThan(0);

            statCards.forEach(card => {
                expect(card).toHaveClass('hover:shadow-xl', 'hover:scale-[1.02]');
            });
        });

        it('stat cards have proper responsive classes', () => {
            const { container } = render(<UsersIndex {...defaultProps} />);

            // Look for the main stat card containers
            const statCards = container.querySelectorAll('[class*="min-h-"]');
            expect(statCards.length).toBeGreaterThan(0);

            statCards.forEach(card => {
                expect(card).toHaveClass('p-4', 'sm:p-6');
                expect(card).toHaveClass('min-h-[120px]', 'sm:min-h-[140px]');
            });
        });
    });

    describe('Color Schemes', () => {
        it('applies gradient colors to stat cards', () => {
            const { container } = render(<UsersIndex {...defaultProps} />);

            const statCards = container.querySelectorAll('[class*="bg-gradient-to-br"]');

            // Should have stat cards with gradient backgrounds
            expect(statCards.length).toBeGreaterThan(0);

            // Each card should have a gradient class
            statCards.forEach(card => {
                const hasGradient = Array.from(card.classList).some(cls =>
                    cls.includes('bg-gradient-to-br')
                );
                expect(hasGradient).toBe(true);
            });
        });
    });
});
