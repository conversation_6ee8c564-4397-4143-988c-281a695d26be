import { usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';

/**
 * Hook to check if the current user can manage content (admin or content manager)
 * Uses the backend role property and admin status
 */
export function useContentManager(): {
    isAdmin: boolean;
    isContentManager: boolean;
    canManageContent: boolean;
} {
    const { auth } = usePage<SharedData>().props;

    try {
        // Check if user exists
        if (!auth?.user) {
            return {
                isAdmin: false,
                isContentManager: false,
                canManageContent: false,
            };
        }

        // Check admin status
        const hasIsAdminMethod = 'isAdmin' in auth.user && typeof auth.user.isAdmin === 'boolean';
        const isAdmin = hasIsAdminMethod
            ? Boolean(auth.user.isAdmin)
            : ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(auth.user.email || '');

        // Check content manager status
        const isContentManager = 'role' in auth.user && auth.user.role === 'content_manager';

        // User can manage content if they are admin or content manager
        const canManageContent = isAdmin || isContentManager;

        return {
            isAdmin,
            isContentManager,
            canManageContent,
        };
    } catch (error) {
        console.error('Error in content manager detection:', error);
        return {
            isAdmin: false,
            isContentManager: false,
            canManageContent: false,
        };
    }
}
