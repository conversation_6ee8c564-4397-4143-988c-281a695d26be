import { useCallback } from 'react';
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';
import type { AnalyticsEvent, CustomEvent, EcommerceEvent } from '@/types/analytics';

/**
 * Hook for tracking common user interactions
 */
export function useInteractionTracking() {
  const { trackEvent } = useAnalytics();

  const trackButtonClick = useCallback((buttonName: string, location?: string) => {
    trackEvent({
      action: 'click',
      category: 'button',
      label: buttonName,
      custom_parameters: {
        location: location || window.location.pathname,
      },
    });
  }, [trackEvent]);

  const trackLinkClick = useCallback((linkUrl: string, linkText?: string) => {
    trackEvent({
      action: 'click',
      category: 'link',
      label: linkUrl,
      custom_parameters: {
        link_text: linkText,
        source_page: window.location.pathname,
      },
    });
  }, [trackEvent]);

  const trackFormSubmission = useCallback((formName: string, success: boolean = true) => {
    trackEvent({
      action: success ? 'submit_success' : 'submit_error',
      category: 'form',
      label: formName,
      custom_parameters: {
        form_location: window.location.pathname,
      },
    });
  }, [trackEvent]);

  const trackModalOpen = useCallback((modalName: string) => {
    trackEvent({
      action: 'open',
      category: 'modal',
      label: modalName,
      custom_parameters: {
        trigger_page: window.location.pathname,
      },
    });
  }, [trackEvent]);

  const trackModalClose = useCallback((modalName: string, method: 'button' | 'escape' | 'overlay' = 'button') => {
    trackEvent({
      action: 'close',
      category: 'modal',
      label: modalName,
      custom_parameters: {
        close_method: method,
      },
    });
  }, [trackEvent]);

  return {
    trackButtonClick,
    trackLinkClick,
    trackFormSubmission,
    trackModalOpen,
    trackModalClose,
  };
}

/**
 * Hook for tracking search-related events
 */
export function useSearchTracking() {
  const { trackEvent, trackCustomEvent } = useAnalytics();

  const trackSearch = useCallback((query: string, resultsCount: number, searchType?: string) => {
    trackCustomEvent({
      event_name: 'search',
      parameters: {
        search_term: query,
        search_type: searchType || 'general',
        results_count: resultsCount,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  const trackSearchResultClick = useCallback((query: string, resultPosition: number, resultId?: string) => {
    trackEvent({
      action: 'click',
      category: 'search_result',
      label: query,
      value: resultPosition,
      custom_parameters: {
        result_id: resultId,
        result_position: resultPosition,
      },
    });
  }, [trackEvent]);

  const trackSearchFilter = useCallback((filterType: string, filterValue: string, query?: string) => {
    trackEvent({
      action: 'filter',
      category: 'search',
      label: `${filterType}:${filterValue}`,
      custom_parameters: {
        filter_type: filterType,
        filter_value: filterValue,
        search_query: query,
      },
    });
  }, [trackEvent]);

  const trackSearchSort = useCallback((sortBy: string, sortOrder: 'asc' | 'desc', query?: string) => {
    trackEvent({
      action: 'sort',
      category: 'search',
      label: `${sortBy}_${sortOrder}`,
      custom_parameters: {
        sort_by: sortBy,
        sort_order: sortOrder,
        search_query: query,
      },
    });
  }, [trackEvent]);

  return {
    trackSearch,
    trackSearchResultClick,
    trackSearchFilter,
    trackSearchSort,
  };
}

/**
 * Hook for tracking e-commerce events
 */
export function useEcommerceTracking() {
  const { trackEcommerce, trackCustomEvent } = useAnalytics();

  const trackPurchase = useCallback((transactionId: string, value: number, currency: string = 'USD', items: any[] = []) => {
    trackEcommerce({
      transaction_id: transactionId,
      value,
      currency,
      items,
    });
  }, [trackEcommerce]);

  const trackAddToCart = useCallback((itemId: string, itemName: string, price: number, quantity: number = 1) => {
    trackCustomEvent({
      event_name: 'add_to_cart',
      parameters: {
        currency: 'USD',
        value: price * quantity,
        items: [{
          item_id: itemId,
          item_name: itemName,
          price,
          quantity,
        }],
      },
    });
  }, [trackCustomEvent]);

  const trackRemoveFromCart = useCallback((itemId: string, itemName: string, price: number, quantity: number = 1) => {
    trackCustomEvent({
      event_name: 'remove_from_cart',
      parameters: {
        currency: 'USD',
        value: price * quantity,
        items: [{
          item_id: itemId,
          item_name: itemName,
          price,
          quantity,
        }],
      },
    });
  }, [trackCustomEvent]);

  const trackViewItem = useCallback((itemId: string, itemName: string, category?: string, price?: number) => {
    trackCustomEvent({
      event_name: 'view_item',
      parameters: {
        currency: 'USD',
        value: price,
        items: [{
          item_id: itemId,
          item_name: itemName,
          category,
          price,
        }],
      },
    });
  }, [trackCustomEvent]);

  const trackBeginCheckout = useCallback((value: number, currency: string = 'USD', items: any[] = []) => {
    trackCustomEvent({
      event_name: 'begin_checkout',
      parameters: {
        currency,
        value,
        items,
      },
    });
  }, [trackCustomEvent]);

  return {
    trackPurchase,
    trackAddToCart,
    trackRemoveFromCart,
    trackViewItem,
    trackBeginCheckout,
  };
}

/**
 * Hook for tracking user engagement events
 */
export function useEngagementTracking() {
  const { trackEvent, trackCustomEvent } = useAnalytics();

  const trackTimeOnPage = useCallback((timeInSeconds: number, pageName?: string) => {
    trackEvent({
      action: 'time_on_page',
      category: 'engagement',
      label: pageName || window.location.pathname,
      value: Math.round(timeInSeconds),
    });
  }, [trackEvent]);

  const trackScrollDepth = useCallback((percentage: number) => {
    trackEvent({
      action: 'scroll',
      category: 'engagement',
      label: `${percentage}%`,
      value: percentage,
    });
  }, [trackEvent]);

  const trackFileDownload = useCallback((fileName: string, fileType: string, fileSize?: number) => {
    trackCustomEvent({
      event_name: 'file_download',
      parameters: {
        file_name: fileName,
        file_type: fileType,
        file_size: fileSize,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  const trackVideoPlay = useCallback((videoTitle: string, videoDuration?: number) => {
    trackCustomEvent({
      event_name: 'video_play',
      parameters: {
        video_title: videoTitle,
        video_duration: videoDuration,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  const trackVideoComplete = useCallback((videoTitle: string, videoDuration?: number) => {
    trackCustomEvent({
      event_name: 'video_complete',
      parameters: {
        video_title: videoTitle,
        video_duration: videoDuration,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  const trackSocialShare = useCallback((platform: string, contentType: string, contentId?: string) => {
    trackEvent({
      action: 'share',
      category: 'social',
      label: platform,
      custom_parameters: {
        content_type: contentType,
        content_id: contentId,
        page_location: window.location.pathname,
      },
    });
  }, [trackEvent]);

  return {
    trackTimeOnPage,
    trackScrollDepth,
    trackFileDownload,
    trackVideoPlay,
    trackVideoComplete,
    trackSocialShare,
  };
}

/**
 * Hook for tracking errors and performance
 */
export function useErrorTracking() {
  const { trackCustomEvent } = useAnalytics();

  const trackError = useCallback((errorMessage: string, errorType: string = 'javascript', fatal: boolean = false) => {
    trackCustomEvent({
      event_name: 'exception',
      parameters: {
        description: errorMessage,
        error_type: errorType,
        fatal,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  const trackApiError = useCallback((endpoint: string, statusCode: number, errorMessage?: string) => {
    trackCustomEvent({
      event_name: 'api_error',
      parameters: {
        endpoint,
        status_code: statusCode,
        error_message: errorMessage,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  const trackPerformance = useCallback((metricName: string, value: number, unit: string = 'ms') => {
    trackCustomEvent({
      event_name: 'performance_metric',
      parameters: {
        metric_name: metricName,
        metric_value: value,
        metric_unit: unit,
        page_location: window.location.pathname,
      },
    });
  }, [trackCustomEvent]);

  return {
    trackError,
    trackApiError,
    trackPerformance,
  };
}
