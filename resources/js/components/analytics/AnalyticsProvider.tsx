import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { usePage } from '@inertiajs/react';
import { initializeAnalytics, getAnalytics } from '@/utils/analytics';
import CookieConsent from './CookieConsent';
import type {
  AnalyticsConfig,
  AnalyticsEvent,
  AnalyticsManager,
  ConsentSettings,
  CustomEvent,
  EcommerceEvent,
  PageViewEvent,
  UserProperties,
} from '@/types/analytics';

interface AnalyticsContextType {
  analytics: AnalyticsManager | null;
  isInitialized: boolean;
  trackPageView: (event?: PageViewEvent) => void;
  trackEvent: (event: AnalyticsEvent) => void;
  trackCustomEvent: (event: CustomEvent) => void;
  trackEcommerce: (event: EcommerceEvent) => void;
  setUserProperties: (properties: UserProperties) => void;
  updateConsent: (settings: ConsentSettings) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

interface AnalyticsProviderProps {
  children: ReactNode;
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  const { props } = usePage();
  const [analytics, setAnalytics] = useState<AnalyticsManager | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showCookieConsent, setShowCookieConsent] = useState(false);

  const analyticsConfig = props.analytics as AnalyticsConfig;

  useEffect(() => {
    initializeAnalyticsService();
  }, []);

  const initializeAnalyticsService = async () => {
    if (!analyticsConfig?.enabled) {
      console.log('[Analytics] Analytics disabled in configuration');
      return;
    }

    try {
      const analyticsManager = initializeAnalytics(analyticsConfig);
      setAnalytics(analyticsManager);

      // Check if consent is required
      if (analyticsConfig.privacy?.cookie_consent_required) {
        // Check if user has already given consent
        const storedConsent = localStorage.getItem('cookie_consent_preferences');
        if (!storedConsent) {
          setShowCookieConsent(true);
          // Initialize analytics but don't track until consent is given
          await analyticsManager.initialize();
          setIsInitialized(true);
          return;
        }

        // Apply stored consent
        try {
          const { preferences } = JSON.parse(storedConsent);
          const consentSettings: ConsentSettings = {
            analytics_storage: preferences.analytics ? 'granted' : 'denied',
            ad_storage: preferences.marketing ? 'granted' : 'denied',
            ad_user_data: preferences.marketing ? 'granted' : 'denied',
            ad_personalization: preferences.marketing ? 'granted' : 'denied',
            functionality_storage: preferences.functional ? 'granted' : 'denied',
            security_storage: 'granted',
          };
          
          await analyticsManager.initialize();
          analyticsManager.updateConsent(consentSettings);
          setIsInitialized(true);
        } catch (error) {
          console.error('[Analytics] Error applying stored consent:', error);
          setShowCookieConsent(true);
        }
      } else {
        // No consent required, initialize directly
        await analyticsManager.initialize();
        setIsInitialized(true);
      }

      console.log('[Analytics] Analytics service initialized');
    } catch (error) {
      console.error('[Analytics] Failed to initialize analytics:', error);
    }
  };

  const handleConsentChange = (consent: ConsentSettings) => {
    if (analytics) {
      analytics.updateConsent(consent);
    }
    setShowCookieConsent(false);
  };

  const trackPageView = (event?: PageViewEvent) => {
    if (analytics && isInitialized) {
      analytics.trackPageView(event);
    }
  };

  const trackEvent = (event: AnalyticsEvent) => {
    if (analytics && isInitialized) {
      analytics.trackEvent(event);
    }
  };

  const trackCustomEvent = (event: CustomEvent) => {
    if (analytics && isInitialized) {
      analytics.trackCustomEvent(event);
    }
  };

  const trackEcommerce = (event: EcommerceEvent) => {
    if (analytics && isInitialized) {
      analytics.trackEcommerce(event);
    }
  };

  const setUserProperties = (properties: UserProperties) => {
    if (analytics && isInitialized) {
      analytics.setUserProperties(properties);
    }
  };

  const updateConsent = (settings: ConsentSettings) => {
    if (analytics) {
      analytics.updateConsent(settings);
    }
  };

  const contextValue: AnalyticsContextType = {
    analytics,
    isInitialized,
    trackPageView,
    trackEvent,
    trackCustomEvent,
    trackEcommerce,
    setUserProperties,
    updateConsent,
  };

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
      {showCookieConsent && (
        <CookieConsent onConsentChange={handleConsentChange} />
      )}
    </AnalyticsContext.Provider>
  );
}

export function useAnalytics(): AnalyticsContextType {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
}

// Hook for tracking page views automatically
export function usePageTracking() {
  const { trackPageView, isInitialized } = useAnalytics();
  const { url } = usePage();

  useEffect(() => {
    if (isInitialized) {
      trackPageView({
        page_location: window.location.href,
        page_title: document.title,
      });
    }
  }, [url, isInitialized, trackPageView]);
}

// Hook for tracking user properties
export function useUserTracking() {
  const { setUserProperties, isInitialized } = useAnalytics();
  const { props } = usePage();

  useEffect(() => {
    if (isInitialized && props.auth?.user) {
      const user = props.auth.user as any;
      setUserProperties({
        user_id: user.id?.toString(),
        user_role: user.role,
        user_type: user.user_type,
        subscription_plan: user.subscription_plan,
      });
    }
  }, [isInitialized, props.auth?.user, setUserProperties]);
}

export default AnalyticsProvider;
