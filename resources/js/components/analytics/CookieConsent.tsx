import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { X, Settings, Shield, BarChart3, Target, Cookie } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { getAnalytics } from '@/utils/analytics';
import type { ConsentSettings } from '@/types/analytics';

interface CookieConsentProps {
  onConsentChange?: (consent: ConsentSettings) => void;
}

interface ConsentPreferences {
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
  necessary: boolean;
}

const CONSENT_STORAGE_KEY = 'cookie_consent_preferences';
const CONSENT_VERSION = '1.0';

export default function CookieConsent({ onConsentChange }: CookieConsentProps) {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<ConsentPreferences>({
    analytics: false,
    marketing: false,
    functional: true,
    necessary: true,
  });

  useEffect(() => {
    checkConsentStatus();
  }, []);

  const checkConsentStatus = () => {
    const stored = localStorage.getItem(CONSENT_STORAGE_KEY);
    if (stored) {
      try {
        const { preferences: storedPrefs, version } = JSON.parse(stored);
        if (version === CONSENT_VERSION) {
          setPreferences(storedPrefs);
          applyConsent(storedPrefs);
          return;
        }
      } catch (error) {
        console.error('Error parsing stored consent:', error);
      }
    }
    
    // Show banner if no valid consent found
    setShowBanner(true);
  };

  const saveConsent = (newPreferences: ConsentPreferences) => {
    const consentData = {
      preferences: newPreferences,
      version: CONSENT_VERSION,
      timestamp: new Date().toISOString(),
    };
    
    localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentData));
    setPreferences(newPreferences);
    applyConsent(newPreferences);
    setShowBanner(false);
    setShowSettings(false);
  };

  const applyConsent = (prefs: ConsentPreferences) => {
    const analytics = getAnalytics();
    if (!analytics) return;

    const consentSettings: ConsentSettings = {
      analytics_storage: prefs.analytics ? 'granted' : 'denied',
      ad_storage: prefs.marketing ? 'granted' : 'denied',
      ad_user_data: prefs.marketing ? 'granted' : 'denied',
      ad_personalization: prefs.marketing ? 'granted' : 'denied',
      functionality_storage: prefs.functional ? 'granted' : 'denied',
      security_storage: 'granted', // Always granted for security
    };

    analytics.updateConsent(consentSettings);
    onConsentChange?.(consentSettings);
  };

  const handleAcceptAll = () => {
    const allAccepted: ConsentPreferences = {
      analytics: true,
      marketing: true,
      functional: true,
      necessary: true,
    };
    saveConsent(allAccepted);
  };

  const handleRejectAll = () => {
    const onlyNecessary: ConsentPreferences = {
      analytics: false,
      marketing: false,
      functional: false,
      necessary: true,
    };
    saveConsent(onlyNecessary);
  };

  const handleSavePreferences = () => {
    saveConsent(preferences);
  };

  const updatePreference = (key: keyof ConsentPreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Cookie Consent Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background border-t shadow-lg">
        <Card className="max-w-4xl mx-auto">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Cookie className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
              <div className="flex-1">
                <CardTitle className="text-lg mb-2">We use cookies</CardTitle>
                <CardDescription className="text-sm mb-4">
                  We use cookies and similar technologies to enhance your browsing experience, 
                  analyze site traffic, and provide personalized content. You can choose which 
                  cookies to accept below.
                </CardDescription>
                <div className="flex flex-wrap gap-3">
                  <Button onClick={handleAcceptAll} size="sm">
                    Accept All
                  </Button>
                  <Button onClick={handleRejectAll} variant="outline" size="sm">
                    Reject All
                  </Button>
                  <Dialog open={showSettings} onOpenChange={setShowSettings}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4 mr-2" />
                        Customize
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Cookie Preferences</DialogTitle>
                        <DialogDescription>
                          Choose which cookies you want to accept. You can change these settings at any time.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-6">
                        {/* Necessary Cookies */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Shield className="h-5 w-5 text-green-600" />
                              <div>
                                <Label className="text-base font-medium">Necessary Cookies</Label>
                                <p className="text-sm text-muted-foreground">
                                  Essential for the website to function properly
                                </p>
                              </div>
                            </div>
                            <Switch
                              checked={preferences.necessary}
                              disabled
                              aria-label="Necessary cookies (always enabled)"
                            />
                          </div>
                          <p className="text-xs text-muted-foreground ml-8">
                            These cookies are essential for the website to function and cannot be disabled. 
                            They include authentication, security, and basic functionality cookies.
                          </p>
                        </div>

                        {/* Functional Cookies */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Settings className="h-5 w-5 text-blue-600" />
                              <div>
                                <Label className="text-base font-medium">Functional Cookies</Label>
                                <p className="text-sm text-muted-foreground">
                                  Remember your preferences and settings
                                </p>
                              </div>
                            </div>
                            <Switch
                              checked={preferences.functional}
                              onCheckedChange={(checked) => updatePreference('functional', checked)}
                              aria-label="Functional cookies"
                            />
                          </div>
                          <p className="text-xs text-muted-foreground ml-8">
                            These cookies remember your preferences like language, theme, and other 
                            customization options to enhance your experience.
                          </p>
                        </div>

                        {/* Analytics Cookies */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <BarChart3 className="h-5 w-5 text-purple-600" />
                              <div>
                                <Label className="text-base font-medium">Analytics Cookies</Label>
                                <p className="text-sm text-muted-foreground">
                                  Help us understand how you use our website
                                </p>
                              </div>
                            </div>
                            <Switch
                              checked={preferences.analytics}
                              onCheckedChange={(checked) => updatePreference('analytics', checked)}
                              aria-label="Analytics cookies"
                            />
                          </div>
                          <p className="text-xs text-muted-foreground ml-8">
                            These cookies collect anonymous information about how you use our website, 
                            helping us improve performance and user experience.
                          </p>
                        </div>

                        {/* Marketing Cookies */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Target className="h-5 w-5 text-orange-600" />
                              <div>
                                <Label className="text-base font-medium">Marketing Cookies</Label>
                                <p className="text-sm text-muted-foreground">
                                  Personalize ads and content based on your interests
                                </p>
                              </div>
                            </div>
                            <Switch
                              checked={preferences.marketing}
                              onCheckedChange={(checked) => updatePreference('marketing', checked)}
                              aria-label="Marketing cookies"
                            />
                          </div>
                          <p className="text-xs text-muted-foreground ml-8">
                            These cookies track your browsing habits to show you relevant 
                            advertisements and personalized content.
                          </p>
                        </div>
                      </div>

                      <div className="flex justify-end gap-3 pt-4 border-t">
                        <Button variant="outline" onClick={() => setShowSettings(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleSavePreferences}>
                          Save Preferences
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBanner(false)}
                className="flex-shrink-0"
                aria-label="Close cookie banner"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
