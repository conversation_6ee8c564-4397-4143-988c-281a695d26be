import type {
  AnalyticsConfig,
  AnalyticsEvent,
  AnalyticsManager,
  ConsentSettings,
  CustomEvent,
  EcommerceEvent,
  PageViewEvent,
  UserProperties,
} from '@/types/analytics';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

class GoogleAnalyticsManager implements AnalyticsManager {
  private config: AnalyticsConfig;
  private initialized = false;
  private consentGiven = false;
  private debugMode = false;

  constructor(config: AnalyticsConfig) {
    this.config = config;
    this.debugMode = config.debug || false;
  }

  /**
   * Initialize Google Analytics
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled || !this.config.measurement_id) {
      this.log('Analytics disabled or measurement ID missing');
      return;
    }

    try {
      // Check if user has Do Not Track enabled
      if (this.shouldRespectDoNotTrack()) {
        this.log('Do Not Track detected, skipping analytics initialization');
        return;
      }

      // Initialize dataLayer
      window.dataLayer = window.dataLayer || [];
      window.gtag = function gtag(...args: any[]) {
        window.dataLayer.push(args);
      };

      // Configure consent mode if enabled
      if (this.config.consent_mode?.enabled) {
        this.initializeConsentMode();
      }

      // Load Google Analytics script
      await this.loadGtagScript();

      // Configure gtag
      window.gtag('js', new Date());
      window.gtag('config', this.config.measurement_id, {
        cookie_domain: this.config.cookie_domain || 'auto',
        cookie_expires: this.config.cookie_expires || 63072000,
        anonymize_ip: this.config.anonymize_ip !== false,
        debug_mode: this.debugMode,
        send_page_view: false, // We'll handle page views manually
      });

      this.initialized = true;
      this.log('Google Analytics initialized successfully');

      // Set up auto-tracking if enabled
      this.setupAutoTracking();

    } catch (error) {
      console.error('Failed to initialize Google Analytics:', error);
    }
  }

  /**
   * Check if analytics is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Track a page view
   */
  trackPageView(event?: PageViewEvent): void {
    if (!this.canTrack()) return;

    const pageViewData: Record<string, any> = {
      page_title: event?.page_title || document.title,
      page_location: event?.page_location || window.location.href,
      page_referrer: event?.page_referrer || document.referrer,
      ...event?.custom_parameters,
    };

    window.gtag('event', 'page_view', pageViewData);
    this.log('Page view tracked', pageViewData);
  }

  /**
   * Track a custom event
   */
  trackEvent(event: AnalyticsEvent): void {
    if (!this.canTrack()) return;

    const eventData: Record<string, any> = {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters,
    };

    // Remove undefined values
    Object.keys(eventData).forEach(key => {
      if (eventData[key] === undefined) {
        delete eventData[key];
      }
    });

    window.gtag('event', event.action, eventData);
    this.log('Event tracked', { action: event.action, ...eventData });
  }

  /**
   * Track a custom event with custom name
   */
  trackCustomEvent(event: CustomEvent): void {
    if (!this.canTrack()) return;

    window.gtag('event', event.event_name, event.parameters || {});
    this.log('Custom event tracked', event);
  }

  /**
   * Track ecommerce events
   */
  trackEcommerce(event: EcommerceEvent): void {
    if (!this.canTrack()) return;

    const ecommerceData: Record<string, any> = {};
    
    if (event.transaction_id) ecommerceData.transaction_id = event.transaction_id;
    if (event.value) ecommerceData.value = event.value;
    if (event.currency) ecommerceData.currency = event.currency;
    if (event.items) ecommerceData.items = event.items;

    window.gtag('event', 'purchase', ecommerceData);
    this.log('Ecommerce event tracked', ecommerceData);
  }

  /**
   * Set user properties
   */
  setUserProperties(properties: UserProperties): void {
    if (!this.canTrack()) return;

    // Set user ID if provided
    if (properties.user_id) {
      window.gtag('config', this.config.measurement_id!, {
        user_id: properties.user_id,
      });
    }

    // Set custom user properties
    const customProperties = { ...properties };
    delete customProperties.user_id;

    if (Object.keys(customProperties).length > 0) {
      window.gtag('event', 'user_properties', customProperties);
    }

    this.log('User properties set', properties);
  }

  /**
   * Update consent settings
   */
  updateConsent(settings: ConsentSettings): void {
    if (!this.initialized || !this.config.consent_mode?.enabled) return;

    window.gtag('consent', 'update', settings);
    this.consentGiven = settings.analytics_storage === 'granted';
    this.log('Consent updated', settings);
  }

  /**
   * Enable debug mode
   */
  enableDebugMode(): void {
    this.debugMode = true;
    if (this.initialized) {
      window.gtag('config', this.config.measurement_id!, {
        debug_mode: true,
      });
    }
  }

  /**
   * Disable debug mode
   */
  disableDebugMode(): void {
    this.debugMode = false;
    if (this.initialized) {
      window.gtag('config', this.config.measurement_id!, {
        debug_mode: false,
      });
    }
  }

  /**
   * Check if tracking is allowed
   */
  private canTrack(): boolean {
    if (!this.initialized) {
      this.log('Analytics not initialized');
      return false;
    }

    if (this.config.consent_mode?.enabled && !this.consentGiven) {
      this.log('Consent not given');
      return false;
    }

    return true;
  }

  /**
   * Check if Do Not Track should be respected
   */
  private shouldRespectDoNotTrack(): boolean {
    if (!this.config.privacy?.respect_do_not_track) return false;
    
    const dnt = navigator.doNotTrack || (window as any).doNotTrack || (navigator as any).msDoNotTrack;
    return dnt === '1' || dnt === 'yes';
  }

  /**
   * Initialize consent mode
   */
  private initializeConsentMode(): void {
    if (!this.config.consent_mode?.enabled) return;

    window.gtag('consent', 'default', this.config.consent_mode.default_settings);
    this.log('Consent mode initialized', this.config.consent_mode.default_settings);
  }

  /**
   * Load the gtag script
   */
  private loadGtagScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (document.querySelector(`script[src*="googletagmanager.com/gtag/js"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.measurement_id}`;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load gtag script'));
      
      document.head.appendChild(script);
    });
  }

  /**
   * Set up automatic tracking
   */
  private setupAutoTracking(): void {
    if (!this.config.auto_track) return;

    // Auto-track external links
    if (this.config.auto_track.external_links) {
      this.setupExternalLinkTracking();
    }

    // Auto-track file downloads
    if (this.config.auto_track.file_downloads) {
      this.setupFileDownloadTracking();
    }

    // Auto-track form submissions
    if (this.config.auto_track.form_submissions) {
      this.setupFormSubmissionTracking();
    }

    // Auto-track scroll depth
    if (this.config.auto_track.scroll_depth) {
      this.setupScrollDepthTracking();
    }
  }

  /**
   * Set up external link tracking
   */
  private setupExternalLinkTracking(): void {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a');
      
      if (link && link.hostname !== window.location.hostname) {
        this.trackEvent({
          action: 'click',
          category: 'external_link',
          label: link.href,
        });
      }
    });
  }

  /**
   * Set up file download tracking
   */
  private setupFileDownloadTracking(): void {
    const downloadExtensions = /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|7z|exe|dmg)$/i;
    
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a');
      
      if (link && downloadExtensions.test(link.href)) {
        this.trackEvent({
          action: 'download',
          category: 'file',
          label: link.href,
        });
      }
    });
  }

  /**
   * Set up form submission tracking
   */
  private setupFormSubmissionTracking(): void {
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      const formName = form.name || form.id || 'unnamed_form';
      
      this.trackEvent({
        action: 'submit',
        category: 'form',
        label: formName,
      });
    });
  }

  /**
   * Set up scroll depth tracking
   */
  private setupScrollDepthTracking(): void {
    let maxScroll = 0;
    const thresholds = [25, 50, 75, 90, 100];
    const tracked = new Set<number>();

    const trackScrollDepth = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );

      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent;
        
        for (const threshold of thresholds) {
          if (scrollPercent >= threshold && !tracked.has(threshold)) {
            tracked.add(threshold);
            this.trackEvent({
              action: 'scroll',
              category: 'engagement',
              label: `${threshold}%`,
              value: threshold,
            });
          }
        }
      }
    };

    let ticking = false;
    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          trackScrollDepth();
          ticking = false;
        });
        ticking = true;
      }
    });
  }

  /**
   * Log debug messages
   */
  private log(message: string, data?: any): void {
    if (this.debugMode) {
      console.log(`[Analytics] ${message}`, data || '');
    }
  }
}

// Export singleton instance
let analyticsInstance: GoogleAnalyticsManager | null = null;

export const initializeAnalytics = (config: AnalyticsConfig): GoogleAnalyticsManager => {
  if (!analyticsInstance) {
    analyticsInstance = new GoogleAnalyticsManager(config);
  }
  return analyticsInstance;
};

export const getAnalytics = (): GoogleAnalyticsManager | null => {
  return analyticsInstance;
};

export default GoogleAnalyticsManager;
