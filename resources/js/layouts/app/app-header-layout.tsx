import { AppContent } from '@/components/app-content';
import { AppHeader } from '@/components/app-header';
import { AppShell } from '@/components/app-shell';
import { GlobalSearchCommand } from '@/components/global-search-command';
import { MobileQuickActions } from '@/components/mobile-quick-actions';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import DynamicNavbar from '@/components/DynamicNavbar';
import AnalyticsProvider, { usePageTracking, useUserTracking } from '@/components/analytics/AnalyticsProvider';
import { type BreadcrumbItem } from '@/types';
import { useAdmin } from '@/hooks/use-admin';
import type { PropsWithChildren } from 'react';

interface AppHeaderLayoutProps {
    breadcrumbs?: BreadcrumbItem[];
    useDynamicNavbar?: boolean;
    showSearch?: boolean;
    showAuthButtons?: boolean;
    fullWidth?: boolean;
}

function AppHeaderLayoutContent({ children, breadcrumbs, useDynamicNavbar = false, showSearch = true, showAuthButtons = true, fullWidth = false }: PropsWithChildren<AppHeaderLayoutProps>) {
    const isAdmin = useAdmin();

    // Initialize analytics tracking
    usePageTracking();
    useUserTracking();

    return (
        <>
            <ImpersonationBanner />
            <AppShell>
                {useDynamicNavbar ? (
                    <DynamicNavbar
                        showSearch={showSearch}
                        showAuthButtons={showAuthButtons}
                    />
                ) : (
                    <AppHeader breadcrumbs={breadcrumbs} />
                )}
                <AppContent fullWidth={fullWidth}>{children}</AppContent>
            </AppShell>
            <GlobalSearchCommand isAdmin={isAdmin} />
            <MobileQuickActions />
        </>
    );
}

export default function AppHeaderLayout({ children, breadcrumbs, useDynamicNavbar = false, showSearch = true, showAuthButtons = true, fullWidth = false }: PropsWithChildren<AppHeaderLayoutProps>) {
    return (
        <AnalyticsProvider>
            <AppHeaderLayoutContent
                breadcrumbs={breadcrumbs}
                useDynamicNavbar={useDynamicNavbar}
                showSearch={showSearch}
                showAuthButtons={showAuthButtons}
                fullWidth={fullWidth}
            >
                {children}
            </AppHeaderLayoutContent>
        </AnalyticsProvider>
    );
}
