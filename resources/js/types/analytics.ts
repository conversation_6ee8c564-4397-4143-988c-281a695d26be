export interface AnalyticsConfig {
  enabled: boolean;
  measurement_id?: string;
  debug?: boolean;
  cookie_domain?: string;
  cookie_expires?: number;
  anonymize_ip?: boolean;
  consent_mode?: ConsentModeConfig;
  auto_track?: AutoTrackConfig;
  privacy?: PrivacyConfig;
}

export interface ConsentModeConfig {
  enabled: boolean;
  default_settings: {
    ad_storage: 'granted' | 'denied';
    analytics_storage: 'granted' | 'denied';
    ad_user_data: 'granted' | 'denied';
    ad_personalization: 'granted' | 'denied';
    functionality_storage: 'granted' | 'denied';
    security_storage: 'granted' | 'denied';
  };
}

export interface AutoTrackConfig {
  page_views: boolean;
  file_downloads: boolean;
  external_links: boolean;
  form_submissions: boolean;
  scroll_depth: boolean;
}

export interface PrivacyConfig {
  cookie_consent_required: boolean;
  respect_do_not_track: boolean;
  data_retention_days: number;
}

export interface ConsentSettings {
  ad_storage?: 'granted' | 'denied';
  analytics_storage?: 'granted' | 'denied';
  ad_user_data?: 'granted' | 'denied';
  ad_personalization?: 'granted' | 'denied';
  functionality_storage?: 'granted' | 'denied';
  security_storage?: 'granted' | 'denied';
}

export interface AnalyticsEvent {
  action: string;
  category?: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

export interface PageViewEvent {
  page_title?: string;
  page_location?: string;
  page_referrer?: string;
  custom_parameters?: Record<string, any>;
}

export interface CustomEvent {
  event_name: string;
  parameters?: Record<string, any>;
}

export interface EcommerceEvent {
  transaction_id?: string;
  value?: number;
  currency?: string;
  items?: EcommerceItem[];
}

export interface EcommerceItem {
  item_id: string;
  item_name: string;
  category?: string;
  quantity?: number;
  price?: number;
}

export interface UserProperties {
  user_id?: string;
  user_role?: string;
  user_type?: string;
  subscription_plan?: string;
  [key: string]: any;
}

export interface AnalyticsManager {
  initialize(): Promise<void>;
  isInitialized(): boolean;
  trackPageView(event?: PageViewEvent): void;
  trackEvent(event: AnalyticsEvent): void;
  trackCustomEvent(event: CustomEvent): void;
  trackEcommerce(event: EcommerceEvent): void;
  setUserProperties(properties: UserProperties): void;
  updateConsent(settings: ConsentSettings): void;
  enableDebugMode(): void;
  disableDebugMode(): void;
}
