import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
    Smartphone,
    Calendar,
    Package,
    Eye,
    EyeOff,
    Lock,
    UserPlus,
    ExternalLink,
    Zap,
    Shield,
    Download,
    Share2,
    Bookmark,
    BookmarkCheck,
    Heart,
    HeartHandshake,
    Star,
    TrendingUp,
    Users,
    Clock,
    AlertCircle,
    CheckCircle,
    Info,
    Search,
    Filter,
    BarChart3,
    Globe,
    MapPin,
    Calendar as CalendarIcon,
    Wrench,
    ShoppingCart,
    MessageCircle,
    Flag
} from 'lucide-react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country?: string;
}

interface Category {
    id: number;
    name: string;
    slug?: string;
}

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number?: string;
    category: Category;
    price?: number;
    currency?: string;
    is_verified: boolean;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number?: string;
    release_year?: number;
    specifications?: Record<string, any>;
    images?: string[];
    image_url?: string;
    brand: Brand;
}

interface Props {
    model: MobileModel;
    visibleParts: Part[];
    totalPartsCount: number;
    hiddenPartsCount: number;
    compatibleModels: MobileModel[];
    isSubscribed: boolean;
    hasUnlimitedAccess: boolean;
    maxVisibleParts: number;
    requiresSignup: boolean;
}

export default function ModelShow({
    model,
    visibleParts,
    totalPartsCount,
    hiddenPartsCount,
    compatibleModels,
    isSubscribed,
    hasUnlimitedAccess,
    maxVisibleParts,
    requiresSignup
}: Props) {
    const renderSpecifications = () => {
        if (!model.specifications || Object.keys(model.specifications).length === 0) {
            return (
                <p className="text-muted-foreground">No specifications available</p>
            );
        }

        return (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(model.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                        <span className="font-medium text-gray-600 capitalize">
                            {key.replace(/_/g, ' ')}
                        </span>
                        <span className="text-gray-900">{String(value)}</span>
                    </div>
                ))}
            </div>
        );
    };

    const renderAccessLimitedMessage = () => {
        if (hasUnlimitedAccess || hiddenPartsCount === 0) return null;

        return (
            <Card className="border-orange-200 bg-orange-50">
                <CardContent className="pt-6">
                    <div className="flex items-center space-x-3">
                        <Lock className="h-5 w-5 text-orange-600" />
                        <div className="flex-1">
                            <h3 className="font-semibold text-orange-900">
                                {hiddenPartsCount} more parts available
                            </h3>
                            <p className="text-sm text-orange-700">
                                {requiresSignup 
                                    ? 'Sign up to view all compatible parts and get full access to our database.'
                                    : 'Subscribe to view all compatible parts and get unlimited access.'
                                }
                            </p>
                        </div>
                        <div className="flex space-x-2">
                            {requiresSignup ? (
                                <>
                                    <Link href="/register">
                                        <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                                            <UserPlus className="h-4 w-4 mr-2" />
                                            Sign Up
                                        </Button>
                                    </Link>
                                    <Link href="/login">
                                        <Button size="sm" variant="outline">
                                            Log In
                                        </Button>
                                    </Link>
                                </>
                            ) : (
                                <Link href="/pricing">
                                    <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                                        <Zap className="h-4 w-4 mr-2" />
                                        Subscribe
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    };

    const renderBlurredParts = () => {
        if (hasUnlimitedAccess || hiddenPartsCount === 0) return null;

        // Create placeholder blurred parts
        const blurredParts = Array.from({ length: Math.min(hiddenPartsCount, 3) }, (_, index) => (
            <Card key={`blurred-${index}`} className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200 opacity-80 z-10" />
                <div className="absolute inset-0 backdrop-blur-sm z-20" />
                <div className="absolute inset-0 flex items-center justify-center z-30">
                    <EyeOff className="h-8 w-8 text-gray-400" />
                </div>
                <CardContent className="p-4 filter blur-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-semibold">Hidden Part Name</h3>
                            <p className="text-sm text-muted-foreground">Part Number: XXX-XXXX</p>
                            <Badge variant="secondary" className="mt-1">Category</Badge>
                        </div>
                        <div className="text-right">
                            <p className="font-semibold">$XX.XX</p>
                            <Badge variant="outline" className="mt-1">
                                <Shield className="h-3 w-3 mr-1" />
                                Verified
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>
        ));

        return (
            <div className="space-y-4">
                <div className="flex items-center space-x-2">
                    <EyeOff className="h-5 w-5 text-gray-500" />
                    <h3 className="text-lg font-semibold text-gray-700">
                        {hiddenPartsCount > 3 ? `${hiddenPartsCount - 3} more` : 'Additional'} parts available
                    </h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {blurredParts}
                </div>
            </div>
        );
    };

    return (
        <AppLayout>
            <Head title={`${model.name} - ${model.brand.name} | Mobile Parts Database`} />
            
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center space-x-4">
                        {model.brand.logo_url && (
                            <img
                                src={model.brand.logo_url}
                                alt={model.brand.name}
                                className="w-12 h-12 object-contain"
                            />
                        )}
                        <div className="flex-1">
                            <h1 className="text-3xl font-bold">{model.name}</h1>
                            <p className="text-lg text-muted-foreground">{model.brand.name}</p>
                        </div>
                        {model.image_url && (
                            <div className="hidden md:block">
                                <img
                                    src={model.image_url}
                                    alt={`${model.brand.name} ${model.name}`}
                                    className="w-24 h-24 object-cover rounded-lg border"
                                    onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                    }}
                                />
                            </div>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Model Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Smartphone className="h-5 w-5" />
                                    <span>Model Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h3 className="font-semibold mb-2">Basic Details</h3>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Model Name</span>
                                                <span className="font-medium">{model.name}</span>
                                            </div>
                                            {model.model_number && (
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Model Number</span>
                                                    <span className="font-medium">{model.model_number}</span>
                                                </div>
                                            )}
                                            {model.release_year && (
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Release Year</span>
                                                    <span className="font-medium">{model.release_year}</span>
                                                </div>
                                            )}
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Brand</span>
                                                <span className="font-medium">{model.brand.name}</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {model.brand.country && (
                                        <div>
                                            <h3 className="font-semibold mb-2">Brand Information</h3>
                                            <div className="space-y-2">
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Country</span>
                                                    <span className="font-medium">{model.brand.country}</span>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Specifications */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Specifications</CardTitle>
                                <CardDescription>Technical specifications for this model</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {renderSpecifications()}
                            </CardContent>
                        </Card>

                        {/* Compatible Parts */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Package className="h-5 w-5" />
                                        <span>Compatible Parts</span>
                                    </div>
                                    <Badge variant="secondary">
                                        {hasUnlimitedAccess ? totalPartsCount : `${visibleParts.length} of ${totalPartsCount}`}
                                    </Badge>
                                </CardTitle>
                                <CardDescription>
                                    Parts that are compatible with this model
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Access Limited Message */}
                                {renderAccessLimitedMessage()}

                                {/* Visible Parts */}
                                {visibleParts.length > 0 ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {visibleParts.map((part) => (
                                            <Card key={part.id} className="hover:shadow-md transition-shadow">
                                                <CardContent className="p-4">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex-1">
                                                            <h3 className="font-semibold">{part.name}</h3>
                                                            {part.part_number && (
                                                                <p className="text-sm text-muted-foreground">
                                                                    Part Number: {part.part_number}
                                                                </p>
                                                            )}
                                                            <Badge variant="secondary" className="mt-1">
                                                                {part.category.name}
                                                            </Badge>
                                                        </div>
                                                        <div className="text-right">
                                                            {part.price && (
                                                                <p className="font-semibold">
                                                                    {part.currency || '$'}{part.price}
                                                                </p>
                                                            )}
                                                            {part.is_verified && (
                                                                <Badge variant="outline" className="mt-1">
                                                                    <Shield className="h-3 w-3 mr-1" />
                                                                    Verified
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-muted-foreground text-center py-8">
                                        No compatible parts found for this model.
                                    </p>
                                )}

                                {/* Blurred Parts */}
                                {renderBlurredParts()}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Quick Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center space-x-2">
                                    <Zap className="h-5 w-5" />
                                    <span>Quick Actions</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="grid grid-cols-2 gap-2">
                                    <Button variant="outline" size="sm" className="flex items-center space-x-2">
                                        <Share2 className="h-4 w-4" />
                                        <span>Share</span>
                                    </Button>
                                    <Button variant="outline" size="sm" className="flex items-center space-x-2">
                                        <Bookmark className="h-4 w-4" />
                                        <span>Save</span>
                                    </Button>
                                    <Button variant="outline" size="sm" className="flex items-center space-x-2">
                                        <Download className="h-4 w-4" />
                                        <span>Export</span>
                                    </Button>
                                    <Button variant="outline" size="sm" className="flex items-center space-x-2">
                                        <Flag className="h-4 w-4" />
                                        <span>Report</span>
                                    </Button>
                                </div>
                                <Separator />
                                <div className="space-y-2">
                                    <Link href={`/search?model=${model.slug || model.id}`}>
                                        <Button variant="ghost" size="sm" className="w-full justify-start">
                                            <Search className="h-4 w-4 mr-2" />
                                            Find Similar Models
                                        </Button>
                                    </Link>
                                    <Link href={`/brands/${model.brand.slug || model.brand.id}`}>
                                        <Button variant="ghost" size="sm" className="w-full justify-start">
                                            <Globe className="h-4 w-4 mr-2" />
                                            View Brand Page
                                        </Button>
                                    </Link>
                                    <Button variant="ghost" size="sm" className="w-full justify-start">
                                        <MessageCircle className="h-4 w-4 mr-2" />
                                        Ask Question
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Model Statistics */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center space-x-2">
                                    <BarChart3 className="h-5 w-5" />
                                    <span>Model Stats</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">{totalPartsCount}</div>
                                        <div className="text-sm text-muted-foreground">Total Parts</div>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <div className="text-2xl font-bold text-green-600">
                                            {visibleParts.filter(p => p.is_verified).length}
                                        </div>
                                        <div className="text-sm text-muted-foreground">Verified</div>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">Availability</span>
                                        <Badge variant="secondary" className="text-xs">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            In Stock
                                        </Badge>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">Popularity</span>
                                        <div className="flex items-center space-x-1">
                                            {[1, 2, 3, 4].map((star) => (
                                                <Star key={star} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                            ))}
                                            <Star className="h-3 w-3 text-gray-300" />
                                        </div>
                                    </div>
                                    {model.release_year && (
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-muted-foreground">Age</span>
                                            <span className="text-sm font-medium">
                                                {new Date().getFullYear() - model.release_year} years
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Compatible Models */}
                        {compatibleModels.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg flex items-center space-x-2">
                                        <Smartphone className="h-5 w-5" />
                                        <span>Compatible Models</span>
                                    </CardTitle>
                                    <CardDescription>
                                        Other models with similar parts
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {compatibleModels.slice(0, 3).map((compatibleModel) => (
                                            <Link
                                                key={compatibleModel.id}
                                                href={`/models/${compatibleModel.slug || compatibleModel.id}`}
                                                className="block p-3 rounded-lg border hover:bg-gray-50 transition-colors"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <h4 className="font-medium">{compatibleModel.name}</h4>
                                                        <p className="text-sm text-muted-foreground">
                                                            {compatibleModel.brand.name}
                                                        </p>
                                                    </div>
                                                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                                                </div>
                                            </Link>
                                        ))}
                                        {compatibleModels.length > 3 && (
                                            <Button variant="ghost" size="sm" className="w-full">
                                                View {compatibleModels.length - 3} more models
                                            </Button>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Recent Activity */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center space-x-2">
                                    <Clock className="h-5 w-5" />
                                    <span>Recent Activity</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-3 text-sm">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <div className="flex-1">
                                            <p className="text-muted-foreground">New part added</p>
                                            <p className="text-xs text-muted-foreground">2 hours ago</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-3 text-sm">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <div className="flex-1">
                                            <p className="text-muted-foreground">Price updated</p>
                                            <p className="text-xs text-muted-foreground">1 day ago</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-3 text-sm">
                                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                        <div className="flex-1">
                                            <p className="text-muted-foreground">Specification verified</p>
                                            <p className="text-xs text-muted-foreground">3 days ago</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Help & Support */}
                        <Card className="border-purple-200 bg-purple-50">
                            <CardContent className="pt-6">
                                <div className="text-center space-y-4">
                                    <HeartHandshake className="h-10 w-10 text-purple-600 mx-auto" />
                                    <div>
                                        <h3 className="font-semibold text-purple-900">Need Help?</h3>
                                        <p className="text-sm text-purple-700">
                                            Can't find what you're looking for? Our support team is here to help.
                                        </p>
                                    </div>
                                    <div className="space-y-2">
                                        <Button variant="outline" size="sm" className="w-full">
                                            <MessageCircle className="h-4 w-4 mr-2" />
                                            Contact Support
                                        </Button>
                                        <Button variant="ghost" size="sm" className="w-full text-purple-700">
                                            <Info className="h-4 w-4 mr-2" />
                                            View FAQ
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Subscription Prompt */}
                        {!hasUnlimitedAccess && (
                            <Card className="border-blue-200 bg-blue-50">
                                <CardContent className="pt-6">
                                    <div className="text-center space-y-4">
                                        <Zap className="h-12 w-12 text-blue-600 mx-auto" />
                                        <div>
                                            <h3 className="font-semibold text-blue-900">
                                                Get Full Access
                                            </h3>
                                            <p className="text-sm text-blue-700">
                                                Subscribe to view all parts, specifications, and get unlimited access to our database.
                                            </p>
                                        </div>
                                        <Link href="/pricing">
                                            <Button className="w-full bg-blue-600 hover:bg-blue-700">
                                                <TrendingUp className="h-4 w-4 mr-2" />
                                                View Pricing Plans
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
