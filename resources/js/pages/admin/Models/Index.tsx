import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Plus,
    Edit,
    Trash2,
    Eye,
    Smartphone,
    Calendar,
    ExternalLink,
    ChevronLeft,
    ChevronRight,
    Search,
    Filter,
    X,
    ArrowUpDown,
    ArrowUp,
    ArrowDown,
    Grid,
    List,
    Table,
    Download,
    Upload,
    FileText
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import { useState, useRef } from 'react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
}

interface ModelSpecifications {
    [key: string]: string | number | boolean | null;
}

interface MobileModel {
    id: number;
    brand_id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    specifications: ModelSpecifications | null;
    images: string[] | null;
    image_url: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    brand: Brand;
    parts_count?: number;
}

interface PaginatedModels {
    data: MobileModel[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface FilterOptions {
    brands: Brand[];
    release_years: number[];
}

interface QueryParams {
    search?: string;
    brand_id?: string;
    status?: string;
    release_year?: string;
    sort_by?: string;
    sort_order?: string;
    view?: string;
}

interface Props {
    models: PaginatedModels;
    filters: FilterOptions;
    queryParams: QueryParams;
}

export default function Index({ models, filters, queryParams }: Props) {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Search and filter state
    const [searchTerm, setSearchTerm] = useState(queryParams.search || '');
    const [selectedBrand, setSelectedBrand] = useState(queryParams.brand_id || 'all');
    const [selectedStatus, setSelectedStatus] = useState(queryParams.status || 'all');
    const [selectedReleaseYear, setSelectedReleaseYear] = useState(queryParams.release_year || 'all');
    const [sortBy, setSortBy] = useState(queryParams.sort_by || 'name');
    const [sortOrder, setSortOrder] = useState(queryParams.sort_order || 'asc');
    const [showFilters, setShowFilters] = useState(false);

    // View mode state - default to table as requested
    const [viewMode, setViewMode] = useState<'list' | 'grid' | 'table'>(
        (queryParams.view as 'list' | 'grid' | 'table') || 'table'
    );

    // Export/Import state
    const [selectedModels, setSelectedModels] = useState<number[]>([]);
    const [showImportDialog, setShowImportDialog] = useState(false);
    const [duplicateAction, setDuplicateAction] = useState<'skip' | 'update' | 'error'>('skip');
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isImporting, setIsImporting] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Check if any filters are active
    const hasActiveFilters = searchTerm || selectedBrand !== 'all' || selectedStatus !== 'all' || selectedReleaseYear !== 'all';

    const handlePageChange = (page: number) => {
        const params = {
            page,
            ...(searchTerm && { search: searchTerm }),
            ...(selectedBrand !== 'all' && { brand_id: selectedBrand }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(selectedReleaseYear !== 'all' && { release_year: selectedReleaseYear }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/models', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSearch = () => {
        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedBrand !== 'all' && { brand_id: selectedBrand }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(selectedReleaseYear !== 'all' && { release_year: selectedReleaseYear }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/models', params, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setSelectedBrand('all');
        setSelectedStatus('all');
        setSelectedReleaseYear('all');
        setSortBy('name');
        setSortOrder('asc');

        router.get('/admin/models', { view: viewMode !== 'table' ? viewMode : undefined }, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleSort = (field: string) => {
        const newSortOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newSortOrder);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedBrand !== 'all' && { brand_id: selectedBrand }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(selectedReleaseYear !== 'all' && { release_year: selectedReleaseYear }),
            sort_by: field,
            sort_order: newSortOrder,
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/models', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleViewModeChange = (newViewMode: 'list' | 'grid' | 'table') => {
        setViewMode(newViewMode);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedBrand !== 'all' && { brand_id: selectedBrand }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(selectedReleaseYear !== 'all' && { release_year: selectedReleaseYear }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(newViewMode !== 'table' && { view: newViewMode }),
        };

        router.get('/admin/models', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDelete = (model: MobileModel) => {
        showDeleteConfirmation({
            title: `Delete "${model.brand.name} ${model.name}"?`,
            description: "This action cannot be undone. All associated parts compatibility will also be removed.",
            onConfirm: () => {
                router.delete(`/admin/models/${model.id}`, {
                    onSuccess: () => {
                        toast.success(`Model "${model.brand.name} ${model.name}" has been deleted successfully.`);
                    },
                    onError: (errors) => {
                        const errorMessage = errors.message || 'Failed to delete model. It may have associated parts.';
                        toast.error(errorMessage);
                    }
                });
            },
            onCancel: () => {
                toast.info('Delete cancelled');
            }
        });
    };

    // Export/Import handlers
    const handleExportAll = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedBrand !== 'all') params.append('brand_id', selectedBrand);
        if (selectedStatus !== 'all') params.append('status', selectedStatus);
        if (selectedReleaseYear !== 'all') params.append('release_year', selectedReleaseYear);
        if (sortBy !== 'name') params.append('sort_by', sortBy);
        if (sortOrder !== 'asc') params.append('sort_order', sortOrder);

        window.location.href = `/admin/models/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleExportSelected = () => {
        if (selectedModels.length === 0) {
            toast.error('Please select models to export');
            return;
        }

        const params = new URLSearchParams();
        selectedModels.forEach(id => params.append('ids[]', id.toString()));

        window.location.href = `/admin/models/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleDownloadTemplate = () => {
        window.location.href = '/admin/models/template/download';
        toast.success('Template download started.');
    };

    const handleImport = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            toast.error('Please select a CSV file');
            return;
        }

        setSelectedFile(file);
        setShowImportDialog(true);
    };

    const handleImportConfirm = () => {
        if (!selectedFile) return;

        setIsImporting(true);
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('duplicate_action', duplicateAction);

        // Add CSRF token manually when using forceFormData
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        router.post('/admin/bulk-import/models', formData, {
            forceFormData: true,
            onSuccess: (page) => {
                // Extract flash messages from the response
                const flashMessages = (page.props?.flash as any) || {};
                const importErrors = (page.props as any)?.import_errors || (flashMessages as any).import_errors;

                // Flash success message will be handled by FlashMessageHandler component
                // Check for import errors in the success response
                if (importErrors && importErrors.length > 0) {
                    toast.warning(`Import completed with ${importErrors.length} errors. Please check the data and try again.`);
                }

                setShowImportDialog(false);
                setSelectedFile(null);
                setIsImporting(false);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                // Refresh the page to show the imported models
                router.reload();
            },
            onError: (errors) => {
                // Handle different types of errors
                if (errors.file) {
                    toast.error(`File error: ${errors.file}`);
                } else if (errors.message) {
                    toast.error(errors.message);
                } else if (errors.import_errors) {
                    toast.error('Import completed with errors. Please check your data and try again.');
                } else {
                    toast.error('Import failed. Please check your CSV format and try again.');
                }
                setIsImporting(false);
            }
        });
    };

    const handleSelectModel = (modelId: number) => {
        setSelectedModels(prev =>
            prev.includes(modelId)
                ? prev.filter(id => id !== modelId)
                : [...prev, modelId]
        );
    };

    const handleSelectAll = () => {
        if (selectedModels.length === models.data.length) {
            setSelectedModels([]);
        } else {
            setSelectedModels(models.data.map(model => model.id));
        }
    };

    // Table View Component
    const ModelTableView = () => (
        <div className="overflow-x-auto">
            <table className="w-full">
                <thead>
                    <tr className="border-b">
                        <th className="text-left p-3 w-12">
                            <Checkbox
                                checked={selectedModels.length === models.data.length && models.data.length > 0}
                                onCheckedChange={handleSelectAll}
                                aria-label="Select all models"
                            />
                        </th>
                        <th className="text-left p-3">
                            <Button
                                variant="ghost"
                                onClick={() => handleSort('name')}
                                className="h-auto p-0 font-semibold hover:bg-transparent"
                            >
                                Model
                                {sortBy === 'name' && (
                                    sortOrder === 'asc' ? (
                                        <ArrowUp className="ml-1 h-4 w-4" />
                                    ) : (
                                        <ArrowDown className="ml-1 h-4 w-4" />
                                    )
                                )}
                                {sortBy !== 'name' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                            </Button>
                        </th>
                        <th className="text-left p-3">Image</th>
                        <th className="text-left p-3">
                            <Button
                                variant="ghost"
                                onClick={() => handleSort('model_number')}
                                className="h-auto p-0 font-semibold hover:bg-transparent"
                            >
                                Model Number
                                {sortBy === 'model_number' && (
                                    sortOrder === 'asc' ? (
                                        <ArrowUp className="ml-1 h-4 w-4" />
                                    ) : (
                                        <ArrowDown className="ml-1 h-4 w-4" />
                                    )
                                )}
                                {sortBy !== 'model_number' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                            </Button>
                        </th>
                        <th className="text-left p-3">
                            <Button
                                variant="ghost"
                                onClick={() => handleSort('release_year')}
                                className="h-auto p-0 font-semibold hover:bg-transparent"
                            >
                                Release Year
                                {sortBy === 'release_year' && (
                                    sortOrder === 'asc' ? (
                                        <ArrowUp className="ml-1 h-4 w-4" />
                                    ) : (
                                        <ArrowDown className="ml-1 h-4 w-4" />
                                    )
                                )}
                                {sortBy !== 'release_year' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                            </Button>
                        </th>
                        <th className="text-left p-3">Status</th>
                        <th className="text-left p-3">Parts</th>
                        <th className="text-right p-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {models.data.map((model) => (
                        <tr key={model.id} className="border-b hover:bg-muted/50">
                            <td className="p-3">
                                <Checkbox
                                    checked={selectedModels.includes(model.id)}
                                    onCheckedChange={() => handleSelectModel(model.id)}
                                    aria-label={`Select ${model.brand.name} ${model.name}`}
                                />
                            </td>
                            <td className="p-3">
                                <div className="flex items-center gap-3">
                                    {model.brand.logo_url && (
                                        <img
                                            src={model.brand.logo_url}
                                            alt={model.brand.name}
                                            className="w-6 h-6 object-contain"
                                        />
                                    )}
                                    <div>
                                        <div className="font-medium">
                                            {model.brand.name} {model.name}
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            Brand: {model.brand.name}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td className="p-3">
                                {model.image_url ? (
                                    <img
                                        src={model.image_url}
                                        alt={`${model.brand.name} ${model.name}`}
                                        className="w-12 h-12 object-cover rounded border"
                                        onError={(e) => {
                                            e.currentTarget.style.display = 'none';
                                        }}
                                    />
                                ) : (
                                    <div className="w-12 h-12 bg-muted rounded border flex items-center justify-center">
                                        <span className="text-xs text-muted-foreground">No Image</span>
                                    </div>
                                )}
                            </td>
                            <td className="p-3">
                                <span className="text-sm">
                                    {model.model_number || '-'}
                                </span>
                            </td>
                            <td className="p-3">
                                {model.release_year ? (
                                    <Badge variant="outline" className="flex items-center gap-1 w-fit">
                                        <Calendar className="h-3 w-3" />
                                        {model.release_year}
                                    </Badge>
                                ) : (
                                    <span className="text-sm text-muted-foreground">-</span>
                                )}
                            </td>
                            <td className="p-3">
                                <Badge variant={model.is_active ? "default" : "secondary"}>
                                    {model.is_active ? "Active" : "Inactive"}
                                </Badge>
                            </td>
                            <td className="p-3">
                                <Badge variant="outline">
                                    {model.parts_count || 0} parts
                                </Badge>
                            </td>
                            <td className="p-3">
                                <div className="flex items-center gap-1 justify-end">
                                    <Link href={route('models.show', model.slug || model.id)}>
                                        <Button variant="outline" size="sm" title="View Public Page">
                                            <ExternalLink className="h-3 w-3" />
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/models/${model.id}`}>
                                        <Button variant="outline" size="sm" title="Admin View">
                                            <Eye className="h-3 w-3" />
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/models/${model.id}/edit`}>
                                        <Button variant="outline" size="sm" title="Edit">
                                            <Edit className="h-3 w-3" />
                                        </Button>
                                    </Link>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-destructive hover:text-destructive"
                                        onClick={() => handleDelete(model)}
                                        title="Delete Model"
                                    >
                                        <Trash2 className="h-3 w-3" />
                                    </Button>
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );

    // Grid View Component
    const ModelGridCard = ({ model }: { model: MobileModel }) => (
        <Card className="h-full">
            <CardContent className="p-4">
                <div className="space-y-3">
                    <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                            {model.brand.logo_url && (
                                <img
                                    src={model.brand.logo_url}
                                    alt={model.brand.name}
                                    className="w-8 h-8 object-contain flex-shrink-0"
                                />
                            )}
                            <div className="min-w-0 flex-1">
                                <h3 className="font-medium truncate">
                                    {model.brand.name} {model.name}
                                </h3>
                                {model.model_number && (
                                    <p className="text-sm text-muted-foreground truncate">
                                        Model: {model.model_number}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Model Image */}
                    {model.image_url && (
                        <div className="flex justify-center">
                            <img
                                src={model.image_url}
                                alt={`${model.brand.name} ${model.name}`}
                                className="w-20 h-20 object-cover rounded border"
                                onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                }}
                            />
                        </div>
                    )}

                    <div className="flex flex-wrap items-center gap-2">
                        <Badge variant={model.is_active ? "default" : "secondary"}>
                            {model.is_active ? "Active" : "Inactive"}
                        </Badge>
                        {model.release_year && (
                            <Badge variant="outline" className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {model.release_year}
                            </Badge>
                        )}
                        <Badge variant="outline">
                            {model.parts_count || 0} parts
                        </Badge>
                    </div>

                    <div className="flex items-center gap-1 pt-2">
                        <Link href={route('models.show', model.slug || model.id)}>
                            <Button variant="outline" size="sm" title="View Public Page">
                                <ExternalLink className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/models/${model.id}`}>
                            <Button variant="outline" size="sm" title="Admin View">
                                <Eye className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/models/${model.id}/edit`}>
                            <Button variant="outline" size="sm" title="Edit">
                                <Edit className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(model)}
                            title="Delete Model"
                        >
                            <Trash2 className="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Models - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Mobile Models</h1>
                        <p className="text-muted-foreground">
                            Manage mobile device models
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        {/* Export/Import Buttons */}
                        <Button variant="outline" onClick={handleDownloadTemplate} size="sm">
                            <FileText className="h-4 w-4 mr-2" />
                            Template
                        </Button>
                        <Button variant="outline" onClick={handleImport} size="sm">
                            <Upload className="h-4 w-4 mr-2" />
                            Import
                        </Button>
                        <Button variant="outline" onClick={handleExportAll} size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export All
                        </Button>
                        {selectedModels.length > 0 && (
                            <Button variant="outline" onClick={handleExportSelected} size="sm">
                                <Download className="h-4 w-4 mr-2" />
                                Export Selected ({selectedModels.length})
                            </Button>
                        )}
                        <Link href="/admin/models/create">
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Model
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="space-y-4">
                            {/* Search Bar */}
                            <div className="flex items-center gap-2">
                                <div className="relative flex-1">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search models, model numbers, or brands..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                handleSearch();
                                            }
                                        }}
                                    />
                                </div>
                                <Button onClick={handleSearch}>
                                    <Search className="h-4 w-4 mr-2" />
                                    Search
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => setShowFilters(!showFilters)}
                                    className={showFilters ? 'bg-muted' : ''}
                                >
                                    <Filter className="h-4 w-4 mr-2" />
                                    Filters
                                    {hasActiveFilters && (
                                        <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                                            !
                                        </Badge>
                                    )}
                                </Button>
                                {hasActiveFilters && (
                                    <Button variant="ghost" onClick={handleClearFilters}>
                                        <X className="h-4 w-4 mr-2" />
                                        Clear
                                    </Button>
                                )}
                            </div>

                            {/* Filter Controls */}
                            {showFilters && (
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                                    <div className="space-y-2">
                                        <Label htmlFor="brand-filter">Brand</Label>
                                        <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                                            <SelectTrigger id="brand-filter">
                                                <SelectValue placeholder="All brands" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All brands</SelectItem>
                                                {filters.brands.map((brand) => (
                                                    <SelectItem key={brand.id} value={brand.id.toString()}>
                                                        {brand.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="status-filter">Status</Label>
                                        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                            <SelectTrigger id="status-filter">
                                                <SelectValue placeholder="All statuses" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All statuses</SelectItem>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="year-filter">Release Year</Label>
                                        <Select value={selectedReleaseYear} onValueChange={setSelectedReleaseYear}>
                                            <SelectTrigger id="year-filter">
                                                <SelectValue placeholder="All years" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All years</SelectItem>
                                                {filters.release_years?.filter(year => year && year.toString().trim() !== '').map((year) => (
                                                    <SelectItem key={year} value={year.toString()}>
                                                        {year}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="sort-filter">Sort By</Label>
                                        <div className="flex gap-2">
                                            <Select value={sortBy} onValueChange={setSortBy}>
                                                <SelectTrigger id="sort-filter" className="flex-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="name">Name</SelectItem>
                                                    <SelectItem value="model_number">Model Number</SelectItem>
                                                    <SelectItem value="release_year">Release Year</SelectItem>
                                                    <SelectItem value="created_at">Created Date</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleSort(sortBy)}
                                                className="px-3"
                                            >
                                                {sortOrder === 'asc' ? (
                                                    <ArrowUp className="h-4 w-4" />
                                                ) : (
                                                    <ArrowDown className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Models List */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Smartphone className="h-5 w-5" />
                                    All Models
                                </CardTitle>
                                <CardDescription>
                                    {models.total} models total
                                    {models.data.length > 0 && (
                                        <span className="ml-2">
                                            (showing {models.from}-{models.to})
                                        </span>
                                    )}
                                </CardDescription>
                            </div>

                            {/* View Mode Toggle */}
                            <div className="flex items-center gap-1 border rounded-lg p-1">
                                <Button
                                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => handleViewModeChange('table')}
                                    className="h-8 px-3"
                                >
                                    <Table className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => handleViewModeChange('list')}
                                    className="h-8 px-3"
                                >
                                    <List className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => handleViewModeChange('grid')}
                                    className="h-8 px-3"
                                >
                                    <Grid className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {models.data.length > 0 ? (
                            <>
                                {/* Conditional rendering based on view mode */}
                                {viewMode === 'table' ? (
                                    <ModelTableView />
                                ) : viewMode === 'grid' ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {models.data.map((model) => (
                                            <ModelGridCard key={model.id} model={model} />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="space-y-4">
                                        {models.data.map((model) => (
                                            <div key={model.id} className="flex items-center justify-between p-4 border rounded-lg">
                                                <div className="space-y-2">
                                                    <div className="flex items-center gap-3">
                                                        {model.brand.logo_url && (
                                                            <img
                                                                src={model.brand.logo_url}
                                                                alt={model.brand.name}
                                                                className="w-6 h-6 object-contain"
                                                            />
                                                        )}
                                                        <div>
                                                            <h3 className="font-medium">
                                                                {model.brand.name} {model.name}
                                                            </h3>
                                                            {model.model_number && (
                                                                <p className="text-sm text-muted-foreground">
                                                                    Model: {model.model_number}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>

                                                    <div className="flex items-center gap-2">
                                                        <Badge variant={model.is_active ? "default" : "secondary"}>
                                                            {model.is_active ? "Active" : "Inactive"}
                                                        </Badge>
                                                        {model.release_year && (
                                                            <Badge variant="outline" className="flex items-center gap-1">
                                                                <Calendar className="h-3 w-3" />
                                                                {model.release_year}
                                                            </Badge>
                                                        )}
                                                        <Badge variant="outline">
                                                            {model.parts_count || 0} parts
                                                        </Badge>
                                                    </div>
                                                </div>

                                                <div className="flex items-center gap-2">
                                                    <Link href={route('models.show', model.slug || model.id)}>
                                                        <Button variant="outline" size="sm" title="View Public Page">
                                                            <ExternalLink className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/models/${model.id}`}>
                                                        <Button variant="outline" size="sm" title="Admin View">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/models/${model.id}/edit`}>
                                                        <Button variant="outline" size="sm" title="Edit">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="text-destructive hover:text-destructive"
                                                        onClick={() => handleDelete(model)}
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-8">
                                <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No models found</h3>
                                <p className="text-muted-foreground mb-4">
                                    {hasActiveFilters
                                        ? "No models match your current filters. Try adjusting your search criteria."
                                        : "Get started by adding your first mobile model."
                                    }
                                </p>
                                {hasActiveFilters ? (
                                    <Button variant="outline" onClick={handleClearFilters}>
                                        <X className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                ) : (
                                    <Link href="/admin/models/create">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add Model
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        )}

                        {/* Pagination */}
                        {models.data.length > 0 && models.last_page > 1 && (
                            <div className="flex items-center justify-between pt-4 border-t">
                                <div className="text-sm text-muted-foreground">
                                    Showing {models.from} to {models.to} of {models.total} models
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(models.current_page - 1)}
                                        disabled={models.current_page <= 1}
                                    >
                                        <ChevronLeft className="h-4 w-4 mr-1" />
                                        Previous
                                    </Button>

                                    <div className="flex items-center gap-1">
                                        {/* Show page numbers */}
                                        {Array.from({ length: Math.min(5, models.last_page) }, (_, i) => {
                                            let pageNum;
                                            if (models.last_page <= 5) {
                                                pageNum = i + 1;
                                            } else {
                                                const start = Math.max(1, models.current_page - 2);
                                                const end = Math.min(models.last_page, start + 4);
                                                pageNum = start + i;
                                                if (pageNum > end) return null;
                                            }

                                            if (pageNum > models.last_page || pageNum < 1) return null;

                                            return (
                                                <Button
                                                    key={pageNum}
                                                    variant={models.current_page === pageNum ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => handlePageChange(pageNum)}
                                                    className="w-8 h-8 p-0"
                                                >
                                                    {pageNum}
                                                </Button>
                                            );
                                        })}
                                    </div>

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(models.current_page + 1)}
                                        disabled={models.current_page >= models.last_page}
                                    >
                                        Next
                                        <ChevronRight className="h-4 w-4 ml-1" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
                </div>
            </div>

            {/* Hidden file input for import */}
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".csv"
                style={{ display: 'none' }}
            />

            {/* Import Dialog */}
            <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Import Models</DialogTitle>
                        <DialogDescription>
                            Upload a CSV file to import mobile models. Make sure your file follows the template format.
                        </DialogDescription>
                    </DialogHeader>

                    {selectedFile && (
                        <div className="space-y-4">
                            <div className="p-4 bg-muted rounded-lg">
                                <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4" />
                                    <span className="font-medium">{selectedFile.name}</span>
                                    <span className="text-sm text-muted-foreground">
                                        ({(selectedFile.size / 1024).toFixed(1)} KB)
                                    </span>
                                </div>
                            </div>

                            <div className="text-sm text-muted-foreground">
                                <p>• Make sure your CSV file has the correct headers</p>
                                <p>• Brand names must match existing brands exactly</p>
                                <p>• Use the template for the correct format</p>
                            </div>

                            {/* Duplicate Action Selector */}
                            <div className="space-y-2">
                                <Label htmlFor="duplicate-action">How should duplicate models be handled?</Label>
                                <Select value={duplicateAction} onValueChange={(value: 'skip' | 'update' | 'error') => setDuplicateAction(value)}>
                                    <SelectTrigger id="duplicate-action">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="skip">Skip duplicates (recommended)</SelectItem>
                                        <SelectItem value="update">Update existing models</SelectItem>
                                        <SelectItem value="error">Report as errors</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p className="text-xs text-muted-foreground">
                                    {duplicateAction === 'skip' && 'Duplicate models will be ignored and not imported.'}
                                    {duplicateAction === 'update' && 'Existing models will be updated with new data.'}
                                    {duplicateAction === 'error' && 'Import will report duplicates as errors.'}
                                </p>
                            </div>

                            {/* Import Status */}
                            {isImporting && (
                                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div className="flex items-center gap-2">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                        <span className="text-sm font-medium text-blue-800">
                                            Processing import...
                                        </span>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setShowImportDialog(false);
                                setSelectedFile(null);
                                if (fileInputRef.current) {
                                    fileInputRef.current.value = '';
                                }
                            }}
                            disabled={isImporting}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleImportConfirm}
                            disabled={!selectedFile || isImporting}
                        >
                            {isImporting ? 'Importing...' : 'Import Models'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
