import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    ArrowLeft,
    Edit,
    Smartphone,
    Package,
    Calendar,
    Building,
    Globe,
    Hash,
    Settings,
    ExternalLink
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country: string | null;
    website: string | null;
    is_active: boolean;
}

interface Category {
    id: number;
    name: string;
    slug?: string;
    description: string | null;
}

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    is_active: boolean;
    category: Category;
    pivot: {
        compatibility_notes: string | null;
        is_verified: boolean;
    };
}

interface MobileModel {
    id: number;
    brand_id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    specifications: Record<string, string> | null;
    images: string[] | null;
    image_url: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    brand: Brand;
    parts?: Part[];
}

interface Props {
    model: MobileModel;
}

export default function Show({ model }: Props) {
    return (
        <AppLayout>
            <Head title={`${model.brand.name} ${model.name} - Models - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href="/admin/models">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Models
                            </Button>
                        </Link>
                        <div className="flex items-center gap-3">
                            {model.brand.logo_url && (
                                <img 
                                    src={model.brand.logo_url} 
                                    alt={model.brand.name}
                                    className="w-8 h-8 object-contain"
                                />
                            )}
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">
                                    {model.brand.name} {model.name}
                                </h1>
                                <p className="text-muted-foreground">
                                    Model details and associated parts
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={route('models.show', model.slug || model.id)}>
                            <Button variant="outline">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Public Page
                            </Button>
                        </Link>
                        <Link href={`/admin/models/${model.id}/edit`}>
                            <Button>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Model
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Model Details */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Smartphone className="h-5 w-5" />
                                Model Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Model Name</label>
                                <p className="text-lg font-medium">{model.name}</p>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Brand</label>
                                <div className="flex items-center gap-2 mt-1">
                                    {model.brand.logo_url && (
                                        <img 
                                            src={model.brand.logo_url} 
                                            alt={model.brand.name}
                                            className="w-5 h-5 object-contain"
                                        />
                                    )}
                                    <p className="text-sm font-medium">{model.brand.name}</p>
                                </div>
                            </div>
                            
                            {model.model_number && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Hash className="h-3 w-3" />
                                        Model Number
                                    </label>
                                    <p className="text-sm">{model.model_number}</p>
                                </div>
                            )}
                            
                            {model.release_year && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Calendar className="h-3 w-3" />
                                        Release Year
                                    </label>
                                    <p className="text-sm">{model.release_year}</p>
                                </div>
                            )}
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Status</label>
                                <div className="mt-1">
                                    <Badge variant={model.is_active ? "default" : "secondary"}>
                                        {model.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </div>
                            </div>

                            {model.image_url && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Model Image</label>
                                    <div className="mt-2">
                                        <img
                                            src={model.image_url}
                                            alt={`${model.brand.name} ${model.name}`}
                                            className="w-32 h-32 object-cover rounded border"
                                            onError={(e) => {
                                                e.currentTarget.style.display = 'none';
                                            }}
                                        />
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building className="h-5 w-5" />
                                Brand Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Brand Name</label>
                                <p className="text-sm font-medium">{model.brand.name}</p>
                            </div>
                            
                            {model.brand.country && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Country</label>
                                    <p className="text-sm">{model.brand.country}</p>
                                </div>
                            )}
                            
                            {model.brand.website && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Globe className="h-3 w-3" />
                                        Website
                                    </label>
                                    <a 
                                        href={model.brand.website} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-sm text-blue-600 hover:underline"
                                    >
                                        {model.brand.website}
                                    </a>
                                </div>
                            )}
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Brand Status</label>
                                <div className="mt-1">
                                    <Badge variant={model.brand.is_active ? "default" : "secondary"}>
                                        {model.brand.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </div>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    Created
                                </label>
                                <p className="text-sm text-muted-foreground">
                                    {new Date(model.created_at).toLocaleDateString()}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Specifications */}
                {model.specifications && Object.keys(model.specifications).length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Settings className="h-5 w-5" />
                                Specifications
                            </CardTitle>
                            <CardDescription>
                                Technical specifications for this model
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {Object.entries(model.specifications).map(([key, value]) => (
                                    <div key={key} className="p-4 border rounded-lg">
                                        <label className="text-sm font-medium text-muted-foreground">{key}</label>
                                        <p className="text-sm font-medium mt-1">{value}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Images */}
                {model.images && model.images.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Images</CardTitle>
                            <CardDescription>
                                Product images for this model
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {model.images.map((image, index) => (
                                    <div key={index} className="border rounded-lg overflow-hidden">
                                        <img 
                                            src={image} 
                                            alt={`${model.name} image ${index + 1}`}
                                            className="w-full h-48 object-cover"
                                            onError={(e) => {
                                                e.currentTarget.src = '/placeholder-image.svg';
                                            }}
                                        />
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Associated Parts */}
                {model.parts && model.parts.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Compatible Parts
                            </CardTitle>
                            <CardDescription>
                                Parts that are compatible with this model ({model.parts.length} parts)
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {model.parts.map((part) => (
                                    <div key={part.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="space-y-1">
                                            <div className="flex items-center gap-2">
                                                <h3 className="font-medium">{part.name}</h3>
                                                <Badge variant="outline">{part.category.name}</Badge>
                                                {part.pivot.is_verified && (
                                                    <Badge variant="default" className="text-xs">Verified</Badge>
                                                )}
                                            </div>
                                            {part.part_number && (
                                                <p className="text-sm text-muted-foreground">
                                                    Part #: {part.part_number}
                                                </p>
                                            )}
                                            {part.manufacturer && (
                                                <p className="text-sm text-muted-foreground">
                                                    Manufacturer: {part.manufacturer}
                                                </p>
                                            )}
                                            {part.pivot.compatibility_notes && (
                                                <p className="text-sm text-muted-foreground">
                                                    Notes: {part.pivot.compatibility_notes}
                                                </p>
                                            )}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge variant={part.is_active ? "default" : "secondary"}>
                                                {part.is_active ? "Active" : "Inactive"}
                                            </Badge>
                                            <Link href={`/admin/parts/${part.id}`}>
                                                <Button variant="outline" size="sm">
                                                    View Part
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* No Parts Message */}
                {(!model.parts || model.parts.length === 0) && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Compatible Parts
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-8">
                                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No compatible parts found</h3>
                                <p className="text-muted-foreground mb-4">
                                    This model doesn't have any compatible parts assigned yet.
                                </p>
                                <Link href="/admin/parts">
                                    <Button variant="outline">
                                        Manage Parts
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                )}
                </div>
            </div>
        </AppLayout>
    );
}
