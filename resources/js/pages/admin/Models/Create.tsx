import { Head, Link, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, Plus, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country: string | null;
    website: string | null;
    is_active: boolean;
}

interface Props {
    brands: Brand[];
}

export default function Create({ brands }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        brand_id: '',
        name: '',
        model_number: '',
        release_year: '',
        specifications: {} as Record<string, string>,
        images: [] as string[],
        image_url: '',
        is_active: true as boolean,
    });

    const [newSpecKey, setNewSpecKey] = useState('');
    const [newSpecValue, setNewSpecValue] = useState('');
    const [newImageUrl, setNewImageUrl] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Flash messages from the backend will be handled by FlashMessageHandler
        post('/admin/models');
    };

    const addSpecification = () => {
        if (newSpecKey && newSpecValue) {
            setData('specifications', {
                ...data.specifications,
                [newSpecKey]: newSpecValue
            });
            setNewSpecKey('');
            setNewSpecValue('');
        }
    };

    const removeSpecification = (key: string) => {
        const newSpecs = { ...data.specifications };
        delete newSpecs[key];
        setData('specifications', newSpecs);
    };

    const addImage = () => {
        if (newImageUrl) {
            setData('images', [...data.images, newImageUrl]);
            setNewImageUrl('');
        }
    };

    const removeImage = (index: number) => {
        const newImages = data.images.filter((_, i) => i !== index);
        setData('images', newImages);
    };

    return (
        <AppLayout>
            <Head title="Create Model - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href="/admin/models">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Models
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Create Model</h1>
                        <p className="text-muted-foreground">
                            Add a new mobile device model
                        </p>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Model Details</CardTitle>
                        <CardDescription>
                            Enter the information for the new mobile model
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Brand Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="brand_id">Brand *</Label>
                                <Select value={data.brand_id} onValueChange={(value) => setData('brand_id', value)}>
                                    <SelectTrigger className={errors.brand_id ? 'border-red-500' : ''}>
                                        <SelectValue placeholder="Select a brand" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {brands.map((brand) => (
                                            <SelectItem key={brand.id} value={brand.id.toString()}>
                                                <div className="flex items-center gap-2">
                                                    {brand.logo_url && (
                                                        <img 
                                                            src={brand.logo_url} 
                                                            alt={brand.name}
                                                            className="w-4 h-4 object-contain"
                                                        />
                                                    )}
                                                    {brand.name}
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.brand_id && (
                                    <p className="text-sm text-red-600">{errors.brand_id}</p>
                                )}
                            </div>

                            {/* Model Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Model Name *</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., iPhone 15 Pro, Galaxy S24 Ultra"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            {/* Model Number */}
                            <div className="space-y-2">
                                <Label htmlFor="model_number">Model Number</Label>
                                <Input
                                    id="model_number"
                                    type="text"
                                    value={data.model_number}
                                    onChange={(e) => setData('model_number', e.target.value)}
                                    placeholder="e.g., A3108, SM-S928B"
                                    className={errors.model_number ? 'border-red-500' : ''}
                                />
                                {errors.model_number && (
                                    <p className="text-sm text-red-600">{errors.model_number}</p>
                                )}
                            </div>

                            {/* Release Year */}
                            <div className="space-y-2">
                                <Label htmlFor="release_year">Release Year</Label>
                                <Input
                                    id="release_year"
                                    type="number"
                                    min="2000"
                                    max={new Date().getFullYear() + 1}
                                    value={data.release_year}
                                    onChange={(e) => setData('release_year', e.target.value)}
                                    placeholder="e.g., 2024"
                                    className={errors.release_year ? 'border-red-500' : ''}
                                />
                                {errors.release_year && (
                                    <p className="text-sm text-red-600">{errors.release_year}</p>
                                )}
                            </div>

                            {/* Specifications */}
                            <div className="space-y-4">
                                <Label>Specifications</Label>
                                <div className="space-y-3">
                                    {Object.entries(data.specifications).map(([key, value]) => (
                                        <div key={key} className="flex items-center gap-2 p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <span className="font-medium text-sm">{key}:</span>
                                                <span className="ml-2 text-sm">{value}</span>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeSpecification(key)}
                                                className="text-destructive hover:text-destructive"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}
                                    
                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Specification name (e.g., Display Size)"
                                            value={newSpecKey}
                                            onChange={(e) => setNewSpecKey(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Input
                                            placeholder="Value (e.g., 6.1 inches)"
                                            value={newSpecValue}
                                            onChange={(e) => setNewSpecValue(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={addSpecification}
                                            disabled={!newSpecKey || !newSpecValue}
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                {errors.specifications && (
                                    <p className="text-sm text-red-600">{errors.specifications}</p>
                                )}
                            </div>

                            {/* Primary Image URL */}
                            <div className="space-y-2">
                                <Label htmlFor="image_url">Primary Image URL</Label>
                                <Input
                                    id="image_url"
                                    type="url"
                                    value={data.image_url}
                                    onChange={(e) => setData('image_url', e.target.value)}
                                    placeholder="https://example.com/image.jpg"
                                    className={errors.image_url ? 'border-red-500' : ''}
                                />
                                {data.image_url && (
                                    <div className="mt-2">
                                        <img
                                            src={data.image_url}
                                            alt="Primary image preview"
                                            className="w-24 h-24 object-cover rounded border"
                                            onError={(e) => {
                                                e.currentTarget.style.display = 'none';
                                            }}
                                        />
                                    </div>
                                )}
                                {errors.image_url && (
                                    <p className="text-sm text-red-600">{errors.image_url}</p>
                                )}
                                <p className="text-sm text-muted-foreground">
                                    URL to the main image for this model (used in exports and listings)
                                </p>
                            </div>

                            {/* Images */}
                            <div className="space-y-4">
                                <Label>Images</Label>
                                <div className="space-y-3">
                                    {data.images.map((image, index) => (
                                        <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <img 
                                                    src={image} 
                                                    alt={`Model image ${index + 1}`}
                                                    className="w-16 h-16 object-cover rounded"
                                                    onError={(e) => {
                                                        e.currentTarget.style.display = 'none';
                                                    }}
                                                />
                                                <p className="text-sm text-muted-foreground mt-1 break-all">{image}</p>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeImage(index)}
                                                className="text-destructive hover:text-destructive"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}
                                    
                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Image URL"
                                            value={newImageUrl}
                                            onChange={(e) => setNewImageUrl(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={addImage}
                                            disabled={!newImageUrl}
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                {errors.images && (
                                    <p className="text-sm text-red-600">{errors.images}</p>
                                )}
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked as boolean)}
                                />
                                <Label htmlFor="is_active">Active</Label>
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex justify-end gap-4 pt-6 border-t">
                                <Link href="/admin/models">
                                    <Button variant="outline" type="button">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="w-4 h-4 mr-2" />
                                    {processing ? 'Creating...' : 'Create Model'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
                </div>
            </div>
        </AppLayout>
    );
}
