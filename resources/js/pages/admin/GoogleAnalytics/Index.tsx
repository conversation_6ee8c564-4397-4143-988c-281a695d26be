import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    BarChart3,
    Settings,
    Shield,
    CheckCircle,
    XCircle,
    AlertTriangle,
    Info,
    ExternalLink,
    TestTube,
    Save,
    RefreshCw,
    Eye,
    Cookie,
    Globe,
    Clock,
    UserX
} from 'lucide-react';

interface GoogleAnalyticsConfig {
    enabled: boolean;
    measurement_id: string;
    debug: boolean;
    cookie_domain: string;
    cookie_expires: number;
    anonymize_ip: boolean;
    consent_mode: boolean;
    cookie_consent_required: boolean;
    respect_dnt: boolean;
    data_retention_days: number;
}

interface Status {
    configured: boolean;
    enabled: boolean;
    measurement_id_valid: boolean;
    status: string;
}

interface Validation {
    valid: boolean;
    errors: string[];
    warnings: string[];
}

interface Props {
    config: GoogleAnalyticsConfig;
    status: Status;
    validation: Validation;
    error_details?: {
        message: string;
        troubleshooting?: string;
        details?: string;
    };
}

export default function GoogleAnalyticsIndex({ config, status, validation, error_details }: Props) {
    const [isTestingConfig, setIsTestingConfig] = useState(false);
    const [showAdvanced, setShowAdvanced] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        enabled: config.enabled,
        measurement_id: config.measurement_id || '',
        debug: config.debug,
        cookie_domain: config.cookie_domain || 'auto',
        cookie_expires: config.cookie_expires || 63072000,
        anonymize_ip: config.anonymize_ip,
        consent_mode: config.consent_mode,
        cookie_consent_required: config.cookie_consent_required,
        respect_dnt: config.respect_dnt,
        data_retention_days: config.data_retention_days || 730,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/google-analytics/config');
    };

    const handleTestConfig = () => {
        setIsTestingConfig(true);
        post('/admin/google-analytics/test', {
            preserveScroll: true,
            onFinish: () => setIsTestingConfig(false),
        });
    };

    const getStatusBadge = () => {
        if (!status.configured) {
            return <Badge variant="secondary"><Settings className="w-3 h-3 mr-1" />Not Configured</Badge>;
        }
        if (!status.enabled) {
            return <Badge variant="outline"><XCircle className="w-3 h-3 mr-1" />Disabled</Badge>;
        }
        if (status.measurement_id_valid) {
            return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
        }
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />Invalid Configuration</Badge>;
    };

    return (
        <AppLayout>
            <Head title="Google Analytics 4 - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <BarChart3 className="w-8 h-8 text-primary" />
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">Google Analytics 4</h1>
                                <p className="text-muted-foreground">
                                    Configure Google Analytics for comprehensive website analytics
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            {getStatusBadge()}
                        </div>
                    </div>

                    {/* Error Alert */}
                    {error_details && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                <div className="space-y-2">
                                    <p className="font-medium">{error_details.message}</p>
                                    {error_details.details && (
                                        <p className="text-sm opacity-90">{error_details.details}</p>
                                    )}
                                    {error_details.troubleshooting && (
                                        <p className="text-sm opacity-90">{error_details.troubleshooting}</p>
                                    )}
                                </div>
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Validation Alerts */}
                    {validation.errors.length > 0 && (
                        <Alert variant="destructive">
                            <XCircle className="h-4 w-4" />
                            <AlertDescription>
                                <div className="space-y-1">
                                    {validation.errors.map((error, index) => (
                                        <p key={index}>{error}</p>
                                    ))}
                                </div>
                            </AlertDescription>
                        </Alert>
                    )}

                    {validation.warnings.length > 0 && (
                        <Alert>
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                <div className="space-y-1">
                                    {validation.warnings.map((warning, index) => (
                                        <p key={index}>{warning}</p>
                                    ))}
                                </div>
                            </AlertDescription>
                        </Alert>
                    )}

                    <Tabs defaultValue="configuration" className="space-y-6">
                        <TabsList>
                            <TabsTrigger value="configuration">Configuration</TabsTrigger>
                            <TabsTrigger value="privacy">Privacy Settings</TabsTrigger>
                            <TabsTrigger value="documentation">Documentation</TabsTrigger>
                        </TabsList>

                        <TabsContent value="configuration" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Basic Configuration
                                    </CardTitle>
                                    <CardDescription>
                                        Configure your Google Analytics 4 measurement ID and basic settings.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* Enable Analytics */}
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="enabled">Enable Google Analytics</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Turn on Google Analytics tracking for your website
                                                </p>
                                            </div>
                                            <Switch
                                                id="enabled"
                                                checked={data.enabled}
                                                onCheckedChange={(checked) => setData('enabled', checked)}
                                            />
                                        </div>

                                        {/* Measurement ID */}
                                        <div className="space-y-2">
                                            <Label htmlFor="measurement_id">
                                                Measurement ID <span className="text-destructive">*</span>
                                            </Label>
                                            <Input
                                                id="measurement_id"
                                                type="text"
                                                placeholder="G-XXXXXXXXXX"
                                                value={data.measurement_id}
                                                onChange={(e) => setData('measurement_id', e.target.value)}
                                                disabled={!data.enabled}
                                                className={errors.measurement_id ? 'border-destructive' : ''}
                                            />
                                            {errors.measurement_id && (
                                                <p className="text-sm text-destructive">{errors.measurement_id}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">
                                                Your Google Analytics 4 Measurement ID (format: G-XXXXXXXXXX)
                                            </p>
                                        </div>

                                        {/* Debug Mode */}
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="debug">Debug Mode</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Enable debug mode for development and testing
                                                </p>
                                            </div>
                                            <Switch
                                                id="debug"
                                                checked={data.debug}
                                                onCheckedChange={(checked) => setData('debug', checked)}
                                                disabled={!data.enabled}
                                            />
                                        </div>

                                        {/* Advanced Settings Toggle */}
                                        <div className="flex items-center justify-between pt-4 border-t">
                                            <div className="space-y-0.5">
                                                <Label>Advanced Settings</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Configure advanced Google Analytics options
                                                </p>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setShowAdvanced(!showAdvanced)}
                                            >
                                                <Eye className="w-4 h-4 mr-2" />
                                                {showAdvanced ? 'Hide' : 'Show'} Advanced
                                            </Button>
                                        </div>

                                        {/* Advanced Settings */}
                                        {showAdvanced && (
                                            <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="cookie_domain">Cookie Domain</Label>
                                                        <Input
                                                            id="cookie_domain"
                                                            type="text"
                                                            placeholder="auto"
                                                            value={data.cookie_domain}
                                                            onChange={(e) => setData('cookie_domain', e.target.value)}
                                                            disabled={!data.enabled}
                                                        />
                                                        <p className="text-xs text-muted-foreground">
                                                            Domain for analytics cookies (default: auto)
                                                        </p>
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="cookie_expires">Cookie Expiration (seconds)</Label>
                                                        <Input
                                                            id="cookie_expires"
                                                            type="number"
                                                            min="0"
                                                            value={data.cookie_expires}
                                                            onChange={(e) => setData('cookie_expires', parseInt(e.target.value) || 0)}
                                                            disabled={!data.enabled}
                                                        />
                                                        <p className="text-xs text-muted-foreground">
                                                            Cookie expiration time (default: 63072000 = 2 years)
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* Action Buttons */}
                                        <div className="flex items-center gap-3 pt-4">
                                            <Button type="submit" disabled={processing}>
                                                <Save className="w-4 h-4 mr-2" />
                                                {processing ? 'Saving...' : 'Save Configuration'}
                                            </Button>

                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={handleTestConfig}
                                                disabled={isTestingConfig || !data.enabled || !data.measurement_id}
                                            >
                                                <TestTube className="w-4 h-4 mr-2" />
                                                {isTestingConfig ? 'Testing...' : 'Test Configuration'}
                                            </Button>

                                            <Button
                                                type="button"
                                                variant="ghost"
                                                onClick={() => reset()}
                                                disabled={processing}
                                            >
                                                <RefreshCw className="w-4 h-4 mr-2" />
                                                Reset
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="privacy" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Shield className="h-5 w-5" />
                                        Privacy & Compliance Settings
                                    </CardTitle>
                                    <CardDescription>
                                        Configure privacy settings to ensure GDPR compliance and user privacy protection.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* IP Anonymization */}
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="anonymize_ip" className="flex items-center gap-2">
                                                    <Globe className="w-4 h-4" />
                                                    IP Anonymization
                                                </Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Anonymize visitor IP addresses for enhanced privacy
                                                </p>
                                            </div>
                                            <Switch
                                                id="anonymize_ip"
                                                checked={data.anonymize_ip}
                                                onCheckedChange={(checked) => setData('anonymize_ip', checked)}
                                                disabled={!data.enabled}
                                            />
                                        </div>

                                        {/* Consent Mode */}
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="consent_mode" className="flex items-center gap-2">
                                                    <Cookie className="w-4 h-4" />
                                                    Consent Mode
                                                </Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Enable Google's Consent Mode for GDPR compliance
                                                </p>
                                            </div>
                                            <Switch
                                                id="consent_mode"
                                                checked={data.consent_mode}
                                                onCheckedChange={(checked) => setData('consent_mode', checked)}
                                                disabled={!data.enabled}
                                            />
                                        </div>

                                        {/* Cookie Consent Required */}
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="cookie_consent_required" className="flex items-center gap-2">
                                                    <Cookie className="w-4 h-4" />
                                                    Require Cookie Consent
                                                </Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Show cookie consent banner before tracking
                                                </p>
                                            </div>
                                            <Switch
                                                id="cookie_consent_required"
                                                checked={data.cookie_consent_required}
                                                onCheckedChange={(checked) => setData('cookie_consent_required', checked)}
                                                disabled={!data.enabled}
                                            />
                                        </div>

                                        {/* Respect Do Not Track */}
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="respect_dnt" className="flex items-center gap-2">
                                                    <UserX className="w-4 h-4" />
                                                    Respect Do Not Track
                                                </Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Honor browser Do Not Track headers
                                                </p>
                                            </div>
                                            <Switch
                                                id="respect_dnt"
                                                checked={data.respect_dnt}
                                                onCheckedChange={(checked) => setData('respect_dnt', checked)}
                                                disabled={!data.enabled}
                                            />
                                        </div>

                                        {/* Data Retention */}
                                        <div className="space-y-2">
                                            <Label htmlFor="data_retention_days" className="flex items-center gap-2">
                                                <Clock className="w-4 h-4" />
                                                Data Retention (days)
                                            </Label>
                                            <Input
                                                id="data_retention_days"
                                                type="number"
                                                min="1"
                                                max="1460"
                                                value={data.data_retention_days}
                                                onChange={(e) => setData('data_retention_days', parseInt(e.target.value) || 730)}
                                                disabled={!data.enabled}
                                            />
                                            <p className="text-sm text-muted-foreground">
                                                How long to retain analytics data (1-1460 days, default: 730)
                                            </p>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex items-center gap-3 pt-4">
                                            <Button type="submit" disabled={processing}>
                                                <Save className="w-4 h-4 mr-2" />
                                                {processing ? 'Saving...' : 'Save Privacy Settings'}
                                            </Button>

                                            <Button
                                                type="button"
                                                variant="ghost"
                                                onClick={() => reset()}
                                                disabled={processing}
                                            >
                                                <RefreshCw className="w-4 h-4 mr-2" />
                                                Reset
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="documentation" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Info className="h-5 w-5" />
                                        Setup Documentation
                                    </CardTitle>
                                    <CardDescription>
                                        Step-by-step guide to configure Google Analytics 4 for your website.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold">Setup Steps</h3>
                                        <ol className="list-decimal list-inside space-y-2 text-sm">
                                            <li>Create a Google Analytics account at <a href="https://analytics.google.com" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline inline-flex items-center gap-1">analytics.google.com <ExternalLink className="w-3 h-3" /></a></li>
                                            <li>Create a new GA4 property for your website</li>
                                            <li>Copy the Measurement ID (format: G-XXXXXXXXXX)</li>
                                            <li>Paste the Measurement ID in the configuration above</li>
                                            <li>Configure privacy settings according to your requirements</li>
                                            <li>Test the configuration to ensure it's working correctly</li>
                                        </ol>
                                    </div>

                                    <Separator />

                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold">Features</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <h4 className="font-medium">Analytics Features</h4>
                                                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                                                    <li>Real-time visitor tracking</li>
                                                    <li>Page view and event analytics</li>
                                                    <li>User behavior insights</li>
                                                    <li>Conversion tracking</li>
                                                </ul>
                                            </div>
                                            <div className="space-y-2">
                                                <h4 className="font-medium">Privacy Features</h4>
                                                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                                                    <li>GDPR-compliant cookie consent</li>
                                                    <li>Do Not Track header respect</li>
                                                    <li>IP address anonymization</li>
                                                    <li>Configurable data retention</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
