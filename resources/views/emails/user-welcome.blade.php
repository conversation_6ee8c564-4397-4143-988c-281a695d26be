@extends('emails.layout')

@section('title', 'Welcome to Mobile Parts DB')

@section('content')
    <div class="greeting">
        Hello {{ $user->name }},
    </div>

    <div class="info-box success">
        <strong>Welcome to Mobile Parts DB!</strong> Your account has been created and you're ready to get started.
    </div>

    <div class="message">
        <p>We're excited to have you join our community! Your account has been successfully created and you now have access to our comprehensive mobile parts database.</p>
        
        @if($password)
        <div class="info-box warning">
            <strong>Important:</strong> Your temporary password is: <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 3px; font-family: monospace;">{{ $password }}</code>
            <br><small>Please change this password after your first login for security purposes.</small>
        </div>
        @endif
        
        <p><strong>What you can do with your account:</strong></p>
        <ul>
            <li>Search our comprehensive mobile parts database</li>
            <li>Access detailed part specifications and compatibility information</li>
            <li>Save your favorite parts for quick reference</li>
            <li>Manage your subscription and account settings</li>
            <li>Track your search history and activity</li>
        </ul>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $loginUrl }}" class="button success">Login to Your Account</a>
    </div>

    <div class="details-table">
        <table class="details-table">
            <tr>
                <th>Account Details</th>
                <th></th>
            </tr>
            <tr>
                <td><strong>Name:</strong></td>
                <td>{{ $user->name }}</td>
            </tr>
            <tr>
                <td><strong>Email:</strong></td>
                <td>{{ $user->email }}</td>
            </tr>
            <tr>
                <td><strong>Role:</strong></td>
                <td>{{ ucfirst(str_replace('_', ' ', $user->role ?? 'user')) }}</td>
            </tr>
            <tr>
                <td><strong>Account Created:</strong></td>
                <td>{{ $user->created_at->format('F j, Y \a\t g:i A') }}</td>
            </tr>
            @if($user->subscription_plan)
            <tr>
                <td><strong>Subscription Plan:</strong></td>
                <td>{{ ucfirst($user->subscription_plan) }}</td>
            </tr>
            @endif
        </table>
    </div>

    <div class="message">
        <p><strong>Getting Started:</strong></p>
        <ol>
            <li>Click the login button above to access your account</li>
            @if($password)
            <li>Use the temporary password provided to log in</li>
            <li>Change your password in your account settings</li>
            @endif
            <li>Explore the parts database and start searching</li>
            <li>Set up your profile and preferences</li>
        </ol>
        
        <p>If you have any questions or need assistance getting started, please don't hesitate to reach out to our support team.</p>
        
        <p>Welcome aboard!</p>
        
        <p>Best regards,<br>
        The Mobile Parts DB Team</p>
    </div>
@endsection
