@extends('emails.layout')

@section('title', 'Password Changed - Mobile Parts DB')

@section('content')
    <div class="greeting">
        Hello {{ $user->name }},
    </div>

    <div class="info-box warning">
        <strong>Password Changed:</strong> Your account password has been updated by an administrator.
    </div>

    <div class="message">
        <p>This is to inform you that your password for Mobile Parts DB has been changed by an administrator.</p>
        
        @if($newPassword)
        <div class="info-box">
            <strong>Your new password is:</strong> <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 3px; font-family: monospace;">{{ $newPassword }}</code>
            <br><small>For security reasons, we recommend changing this password after your next login.</small>
        </div>
        @endif
        
        <p><strong>Security Information:</strong></p>
        <ul>
            <li>Password changed on: {{ $changeTime }}</li>
            <li>If you did not request this change, please contact support immediately</li>
            <li>We recommend using a strong, unique password for your account</li>
            <li>Consider enabling two-factor authentication for additional security</li>
        </ul>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $loginUrl }}" class="button">Login with New Password</a>
    </div>

    <div class="details-table">
        <table class="details-table">
            <tr>
                <th>Account Information</th>
                <th></th>
            </tr>
            <tr>
                <td><strong>Name:</strong></td>
                <td>{{ $user->name }}</td>
            </tr>
            <tr>
                <td><strong>Email:</strong></td>
                <td>{{ $user->email }}</td>
            </tr>
            <tr>
                <td><strong>Password Changed:</strong></td>
                <td>{{ $changeTime }}</td>
            </tr>
            <tr>
                <td><strong>Account Status:</strong></td>
                <td>{{ ucfirst($user->status ?? 'active') }}</td>
            </tr>
        </table>
    </div>

    <div class="message">
        <p><strong>What to do next:</strong></p>
        <ol>
            <li>Use your new password to log in to your account</li>
            <li>Consider changing your password to something memorable but secure</li>
            <li>Review your account security settings</li>
            <li>Contact support if you have any concerns</li>
        </ol>
        
        <p>If you did not request this password change or have any security concerns, please contact our support team immediately.</p>
        
        <p>Best regards,<br>
        The Mobile Parts DB Team</p>
    </div>
@endsection
