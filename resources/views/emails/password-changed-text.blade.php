Password Changed - Mobile Parts DB

Hello {{ $user->name }},

This is to inform you that your password for Mobile Parts DB has been changed by an administrator.

@if($newPassword)
Your new password is: {{ $newPassword }}
For security reasons, we recommend changing this password after your next login.
@endif

Security Information:
- Password changed on: {{ $changeTime }}
- If you did not request this change, please contact support immediately
- We recommend using a strong, unique password for your account
- Consider enabling two-factor authentication for additional security

Account Information:
- Name: {{ $user->name }}
- Email: {{ $user->email }}
- Password Changed: {{ $changeTime }}
- Account Status: {{ ucfirst($user->status ?? 'active') }}

What to do next:
1. Use your new password to log in to your account at {{ $loginUrl }}
2. Consider changing your password to something memorable but secure
3. Review your account security settings
4. Contact support if you have any concerns

If you did not request this password change or have any security concerns, please contact our support team immediately at {{ $supportUrl }}.

Best regards,
The Mobile Parts DB Team

---
This email was sent from Mobile Parts DB.
Visit our website: {{ $appUrl }}
