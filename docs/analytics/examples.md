# Google Analytics Usage Examples

This document provides practical examples for implementing Google Analytics tracking in various scenarios.

## Table of Contents

1. [Basic Page Tracking](#basic-page-tracking)
2. [User Interaction Tracking](#user-interaction-tracking)
3. [E-commerce Tracking](#e-commerce-tracking)
4. [Search and Filtering](#search-and-filtering)
5. [Form Tracking](#form-tracking)
6. [Error and Performance Tracking](#error-and-performance-tracking)
7. [Custom Events](#custom-events)
8. [Advanced Use Cases](#advanced-use-cases)

## Basic Page Tracking

### Automatic Page View Tracking

Page views are automatically tracked when using the analytics provider in your layout:

```tsx
// resources/js/layouts/app/app-sidebar-layout.tsx
import { usePageTracking, useUserTracking } from '@/components/analytics/AnalyticsProvider';

export default function AppSidebarLayout({ children, breadcrumbs = [] }) {
    // Automatically tracks page views and user properties
    usePageTracking();
    useUserTracking();

    return (
        // Your layout JSX
    );
}
```

### Manual Page View Tracking

For custom page view tracking:

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function CustomPageComponent() {
    const { trackPageView } = useAnalytics();

    useEffect(() => {
        trackPageView({
            page_title: 'Custom Page Title',
            page_location: window.location.href,
            content_group1: 'Product Pages',
            content_group2: 'Mobile Devices',
        });
    }, []);

    return <div>Custom page content</div>;
}
```

## User Interaction Tracking

### Button and Link Tracking

```tsx
import { useInteractionTracking } from '@/hooks/use-analytics-tracking';

function NavigationComponent() {
    const { trackButtonClick, trackLinkClick } = useInteractionTracking();

    return (
        <nav>
            <button
                onClick={() => trackButtonClick('main-cta', 'header')}
                className="btn-primary"
            >
                Get Started
            </button>
            
            <a
                href="https://external-site.com"
                onClick={() => trackLinkClick('https://external-site.com', 'External Partner')}
                target="_blank"
                rel="noopener noreferrer"
            >
                External Link
            </a>
        </nav>
    );
}
```

### Modal and Dialog Tracking

```tsx
import { useInteractionTracking } from '@/hooks/use-analytics-tracking';

function ProductModal({ isOpen, onClose, product }) {
    const { trackModalOpen, trackModalClose } = useInteractionTracking();

    useEffect(() => {
        if (isOpen) {
            trackModalOpen(`product-details-${product.id}`);
        }
    }, [isOpen, product.id]);

    const handleClose = (method: 'button' | 'escape' | 'overlay') => {
        trackModalClose(`product-details-${product.id}`, method);
        onClose();
    };

    return (
        <Dialog open={isOpen} onClose={() => handleClose('overlay')}>
            <div>
                <button onClick={() => handleClose('button')}>
                    Close
                </button>
                {/* Modal content */}
            </div>
        </Dialog>
    );
}
```

## E-commerce Tracking

### Product View Tracking

```tsx
import { useEcommerceTracking } from '@/hooks/use-analytics-tracking';

function ProductDetailPage({ product }) {
    const { trackViewItem } = useEcommerceTracking();

    useEffect(() => {
        trackViewItem(
            product.id,
            product.name,
            product.category,
            product.price
        );
    }, [product]);

    return (
        <div>
            <h1>{product.name}</h1>
            <p>Price: ${product.price}</p>
            {/* Product details */}
        </div>
    );
}
```

### Shopping Cart Tracking

```tsx
import { useEcommerceTracking } from '@/hooks/use-analytics-tracking';

function AddToCartButton({ product, quantity = 1 }) {
    const { trackAddToCart } = useEcommerceTracking();

    const handleAddToCart = () => {
        // Add to cart logic
        addToCart(product, quantity);
        
        // Track the event
        trackAddToCart(
            product.id,
            product.name,
            product.price,
            quantity
        );
    };

    return (
        <button onClick={handleAddToCart}>
            Add to Cart
        </button>
    );
}
```

### Checkout Process Tracking

```tsx
import { useEcommerceTracking } from '@/hooks/use-analytics-tracking';

function CheckoutPage({ cartItems }) {
    const { trackBeginCheckout, trackPurchase } = useEcommerceTracking();

    useEffect(() => {
        // Track checkout initiation
        const totalValue = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        trackBeginCheckout(totalValue, 'USD', cartItems.map(item => ({
            item_id: item.product_id,
            item_name: item.product_name,
            category: item.category,
            quantity: item.quantity,
            price: item.price,
        })));
    }, [cartItems]);

    const handlePurchaseComplete = (order) => {
        trackPurchase(
            order.id,
            order.total,
            order.currency,
            order.items.map(item => ({
                item_id: item.product_id,
                item_name: item.product_name,
                category: item.category,
                quantity: item.quantity,
                price: item.price,
                discount: item.discount,
            }))
        );
    };

    return (
        // Checkout form JSX
    );
}
```

## Search and Filtering

### Search Results Tracking

```tsx
import { useSearchTracking } from '@/hooks/use-analytics-tracking';

function SearchResultsPage({ query, results, searchType = 'product' }) {
    const { trackSearch, trackSearchResultClick } = useSearchTracking();

    useEffect(() => {
        trackSearch(query, results.length, searchType);
    }, [query, results.length, searchType]);

    const handleResultClick = (result, position) => {
        trackSearchResultClick(query, position + 1, result.id);
        // Navigate to result
    };

    return (
        <div>
            <h2>Search Results for "{query}"</h2>
            {results.map((result, index) => (
                <div
                    key={result.id}
                    onClick={() => handleResultClick(result, index)}
                >
                    {result.title}
                </div>
            ))}
        </div>
    );
}
```

### Filter and Sort Tracking

```tsx
import { useSearchTracking } from '@/hooks/use-analytics-tracking';

function ProductFilters({ currentQuery, onFilterChange, onSortChange }) {
    const { trackSearchFilter, trackSearchSort } = useSearchTracking();

    const handleFilterChange = (filterType, filterValue) => {
        trackSearchFilter(filterType, filterValue, currentQuery);
        onFilterChange(filterType, filterValue);
    };

    const handleSortChange = (sortBy, sortOrder) => {
        trackSearchSort(sortBy, sortOrder, currentQuery);
        onSortChange(sortBy, sortOrder);
    };

    return (
        <div>
            <select onChange={(e) => handleFilterChange('brand', e.target.value)}>
                <option value="">All Brands</option>
                <option value="apple">Apple</option>
                <option value="samsung">Samsung</option>
            </select>
            
            <select onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('_');
                handleSortChange(sortBy, sortOrder);
            }}>
                <option value="name_asc">Name A-Z</option>
                <option value="price_asc">Price Low-High</option>
                <option value="price_desc">Price High-Low</option>
            </select>
        </div>
    );
}
```

## Form Tracking

### Contact Form Tracking

```tsx
import { useInteractionTracking } from '@/hooks/use-analytics-tracking';

function ContactForm() {
    const { trackFormSubmission } = useInteractionTracking();

    const handleSubmit = async (formData) => {
        try {
            await submitContactForm(formData);
            trackFormSubmission('contact-form', true);
            // Show success message
        } catch (error) {
            trackFormSubmission('contact-form', false);
            // Show error message
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            {/* Form fields */}
            <button type="submit">Submit</button>
        </form>
    );
}
```

### Multi-step Form Tracking

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function MultiStepForm() {
    const { trackCustomEvent } = useAnalytics();
    const [currentStep, setCurrentStep] = useState(1);

    const handleStepChange = (newStep) => {
        trackCustomEvent({
            event_name: 'form_step_change',
            parameters: {
                form_name: 'registration',
                from_step: currentStep,
                to_step: newStep,
                step_direction: newStep > currentStep ? 'forward' : 'backward',
            },
        });
        setCurrentStep(newStep);
    };

    const handleStepComplete = (step) => {
        trackCustomEvent({
            event_name: 'form_step_complete',
            parameters: {
                form_name: 'registration',
                step_number: step,
                completion_time: Date.now() - stepStartTime,
            },
        });
    };

    return (
        // Multi-step form JSX
    );
}
```

## Error and Performance Tracking

### Error Boundary with Analytics

```tsx
import { useErrorTracking } from '@/hooks/use-analytics-tracking';

class AnalyticsErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Track the error
        this.props.trackError(
            `${error.name}: ${error.message}`,
            'react_error_boundary',
            true
        );
    }

    render() {
        if (this.state.hasError) {
            return <ErrorFallback />;
        }

        return this.props.children;
    }
}

// Usage with hook
function ErrorBoundaryWrapper({ children }) {
    const { trackError } = useErrorTracking();
    
    return (
        <AnalyticsErrorBoundary trackError={trackError}>
            {children}
        </AnalyticsErrorBoundary>
    );
}
```

### API Error Tracking

```tsx
import { useErrorTracking } from '@/hooks/use-analytics-tracking';

function useApiCall() {
    const { trackApiError } = useErrorTracking();

    const makeApiCall = async (endpoint, options) => {
        try {
            const response = await fetch(endpoint, options);
            
            if (!response.ok) {
                trackApiError(endpoint, response.status, response.statusText);
                throw new Error(`API Error: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (error.name === 'TypeError') {
                trackApiError(endpoint, 0, 'Network Error');
            }
            throw error;
        }
    };

    return { makeApiCall };
}
```

### Performance Tracking

```tsx
import { useErrorTracking } from '@/hooks/use-analytics-tracking';

function usePerformanceTracking() {
    const { trackPerformance } = useErrorTracking();

    const trackPageLoad = () => {
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            
            trackPerformance('page_load_time', loadTime, 'ms');
        }
    };

    const trackApiResponseTime = (endpoint, startTime) => {
        const responseTime = Date.now() - startTime;
        trackPerformance('api_response_time', responseTime, 'ms');
    };

    useEffect(() => {
        // Track page load time when component mounts
        if (document.readyState === 'complete') {
            trackPageLoad();
        } else {
            window.addEventListener('load', trackPageLoad);
            return () => window.removeEventListener('load', trackPageLoad);
        }
    }, []);

    return { trackApiResponseTime };
}
```

## Custom Events

### User Engagement Events

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function VideoPlayer({ videoId, videoTitle }) {
    const { trackCustomEvent } = useAnalytics();
    const [playStartTime, setPlayStartTime] = useState(null);

    const handlePlay = () => {
        setPlayStartTime(Date.now());
        trackCustomEvent({
            event_name: 'video_play',
            parameters: {
                video_id: videoId,
                video_title: videoTitle,
                video_provider: 'internal',
            },
        });
    };

    const handlePause = () => {
        if (playStartTime) {
            const watchTime = Date.now() - playStartTime;
            trackCustomEvent({
                event_name: 'video_pause',
                parameters: {
                    video_id: videoId,
                    watch_time: watchTime,
                },
            });
        }
    };

    const handleComplete = () => {
        trackCustomEvent({
            event_name: 'video_complete',
            parameters: {
                video_id: videoId,
                video_title: videoTitle,
            },
        });
    };

    return (
        <video
            onPlay={handlePlay}
            onPause={handlePause}
            onEnded={handleComplete}
        >
            {/* Video content */}
        </video>
    );
}
```

### Feature Usage Tracking

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function AdvancedFeatureComponent() {
    const { trackCustomEvent } = useAnalytics();

    const trackFeatureUsage = (featureName, featureValue = null) => {
        trackCustomEvent({
            event_name: 'feature_usage',
            parameters: {
                feature_name: featureName,
                feature_value: featureValue,
                user_segment: getUserSegment(), // Custom function
                page_location: window.location.pathname,
            },
        });
    };

    const handleAdvancedAction = () => {
        trackFeatureUsage('advanced_search', 'filters_applied');
        // Perform advanced action
    };

    return (
        <button onClick={handleAdvancedAction}>
            Use Advanced Feature
        </button>
    );
}
```

## Advanced Use Cases

### A/B Testing Integration

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function ABTestComponent({ variant }) {
    const { trackCustomEvent, setUserProperties } = useAnalytics();

    useEffect(() => {
        // Set user property for A/B test
        setUserProperties({
            ab_test_variant: variant,
        });

        // Track A/B test exposure
        trackCustomEvent({
            event_name: 'ab_test_exposure',
            parameters: {
                test_name: 'homepage_cta_test',
                variant_name: variant,
                exposure_time: new Date().toISOString(),
            },
        });
    }, [variant]);

    const handleConversion = () => {
        trackCustomEvent({
            event_name: 'ab_test_conversion',
            parameters: {
                test_name: 'homepage_cta_test',
                variant_name: variant,
                conversion_type: 'signup',
            },
        });
    };

    return (
        <div>
            {variant === 'A' ? (
                <button onClick={handleConversion}>Sign Up Now</button>
            ) : (
                <button onClick={handleConversion}>Get Started Today</button>
            )}
        </div>
    );
}
```

### User Journey Tracking

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function useUserJourney() {
    const { trackCustomEvent } = useAnalytics();

    const trackJourneyStep = (stepName, stepData = {}) => {
        trackCustomEvent({
            event_name: 'user_journey_step',
            parameters: {
                journey_step: stepName,
                session_id: getSessionId(), // Custom function
                user_id: getCurrentUserId(), // Custom function
                timestamp: Date.now(),
                ...stepData,
            },
        });
    };

    const trackJourneyComplete = (journeyName, totalSteps, completionTime) => {
        trackCustomEvent({
            event_name: 'user_journey_complete',
            parameters: {
                journey_name: journeyName,
                total_steps: totalSteps,
                completion_time: completionTime,
                success: true,
            },
        });
    };

    return { trackJourneyStep, trackJourneyComplete };
}
```

These examples demonstrate the flexibility and power of the Google Analytics integration. You can adapt these patterns to track any user interaction or business metric that's important for your application.
