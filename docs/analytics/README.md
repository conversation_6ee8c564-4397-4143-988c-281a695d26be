# Google Analytics 4 Integration

This document provides comprehensive information about the Google Analytics 4 (GA4) integration implemented in this Laravel application with React TypeScript frontend.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Setup and Configuration](#setup-and-configuration)
4. [Usage](#usage)
5. [Privacy and GDPR Compliance](#privacy-and-gdpr-compliance)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)

## Overview

The Google Analytics integration provides:

- **Modern GA4 Implementation**: Uses the latest Google Analytics 4 with gtag.js
- **TypeScript Support**: Fully typed analytics utilities and components
- **GDPR Compliance**: Built-in cookie consent management
- **Privacy-First**: Respects Do Not Track and provides granular consent controls
- **Auto-tracking**: Automatic page views, user interactions, and engagement metrics
- **Custom Events**: Easy-to-use hooks for tracking custom events
- **Server-Side Configuration**: Laravel backend configuration with environment variables

## Features

### Core Features

- ✅ Google Analytics 4 integration with gtag.js
- ✅ TypeScript support with `@types/gtag.js`
- ✅ GDPR-compliant cookie consent banner
- ✅ Consent mode for privacy-compliant tracking
- ✅ Automatic page view tracking with Inertia.js
- ✅ User property tracking (role, subscription, etc.)
- ✅ Custom event tracking hooks
- ✅ E-commerce event tracking
- ✅ Error and performance tracking
- ✅ Debug mode for development
- ✅ Do Not Track respect
- ✅ Comprehensive test coverage

### Auto-tracking Features

- **Page Views**: Automatic tracking with Inertia.js navigation
- **External Links**: Clicks on external links
- **File Downloads**: Downloads of common file types
- **Form Submissions**: Form submission events
- **Scroll Depth**: User scroll engagement (25%, 50%, 75%, 90%, 100%)

## Setup and Configuration

### 1. Environment Configuration

Add the following variables to your `.env` file:

```env
# Google Analytics Configuration
GOOGLE_ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_MEASUREMENT_ID=G-XXXXXXXXXX
GOOGLE_ANALYTICS_DEBUG=false

# Optional: Advanced Configuration
GOOGLE_ANALYTICS_COOKIE_DOMAIN=auto
GOOGLE_ANALYTICS_COOKIE_EXPIRES=63072000
GOOGLE_ANALYTICS_ANONYMIZE_IP=true
GOOGLE_ANALYTICS_CONSENT_MODE=true

# Privacy Settings
ANALYTICS_COOKIE_CONSENT_REQUIRED=true
ANALYTICS_RESPECT_DNT=true
ANALYTICS_DATA_RETENTION_DAYS=730
```

### 2. Google Analytics Setup

1. Create a Google Analytics 4 property in [Google Analytics](https://analytics.google.com/)
2. Get your Measurement ID (format: `G-XXXXXXXXXX`)
3. Add the Measurement ID to your `.env` file
4. Configure data retention and privacy settings in GA4 dashboard

### 3. Backend Configuration

The analytics configuration is automatically loaded through:

- `config/analytics.php` - Main configuration file
- `app/Services/GoogleAnalyticsService.php` - Service class
- `app/Http/Middleware/InjectAnalyticsConfig.php` - Middleware for Inertia

### 4. Frontend Integration

The analytics provider is automatically integrated into the app through:

- `resources/js/app.tsx` - Analytics provider wrapper
- `resources/js/layouts/app/app-sidebar-layout.tsx` - Auto-tracking hooks

## Usage

### Basic Tracking

The analytics system automatically tracks:

- Page views when navigating with Inertia.js
- User properties when authenticated
- Basic user interactions (if auto-tracking is enabled)

### Custom Event Tracking

Use the provided hooks for custom event tracking:

```tsx
import { useInteractionTracking, useSearchTracking, useEcommerceTracking } from '@/hooks/use-analytics-tracking';

function MyComponent() {
  const { trackButtonClick } = useInteractionTracking();
  const { trackSearch } = useSearchTracking();
  const { trackAddToCart } = useEcommerceTracking();

  const handleButtonClick = () => {
    trackButtonClick('header-cta', '/landing-page');
  };

  const handleSearch = (query: string, results: number) => {
    trackSearch(query, results, 'product');
  };

  const handleAddToCart = (product: Product) => {
    trackAddToCart(product.id, product.name, product.price);
  };

  return (
    // Your component JSX
  );
}
```

### Direct Analytics Access

For advanced use cases, access the analytics manager directly:

```tsx
import { useAnalytics } from '@/components/analytics/AnalyticsProvider';

function AdvancedComponent() {
  const { trackCustomEvent, setUserProperties } = useAnalytics();

  const trackCustomAction = () => {
    trackCustomEvent({
      event_name: 'custom_action',
      parameters: {
        action_type: 'advanced',
        user_segment: 'premium',
        value: 100,
      },
    });
  };

  const updateUserData = (user: User) => {
    setUserProperties({
      user_id: user.id.toString(),
      user_role: user.role,
      subscription_plan: user.subscription_plan,
    });
  };

  return (
    // Your component JSX
  );
}
```

### E-commerce Tracking

Track e-commerce events for business insights:

```tsx
import { useEcommerceTracking } from '@/hooks/use-analytics-tracking';

function CheckoutComponent() {
  const { trackPurchase, trackBeginCheckout, trackViewItem } = useEcommerceTracking();

  const handlePurchase = (order: Order) => {
    trackPurchase(
      order.id,
      order.total,
      order.currency,
      order.items.map(item => ({
        item_id: item.product_id,
        item_name: item.product_name,
        category: item.category,
        quantity: item.quantity,
        price: item.price,
      }))
    );
  };

  return (
    // Your component JSX
  );
}
```

### Error Tracking

Track errors and performance metrics:

```tsx
import { useErrorTracking } from '@/hooks/use-analytics-tracking';

function ErrorBoundary() {
  const { trackError, trackApiError, trackPerformance } = useErrorTracking();

  const handleError = (error: Error) => {
    trackError(error.message, 'javascript', true);
  };

  const handleApiError = (endpoint: string, status: number) => {
    trackApiError(endpoint, status, 'Request failed');
  };

  const trackPageLoad = (loadTime: number) => {
    trackPerformance('page_load_time', loadTime, 'ms');
  };

  return (
    // Your component JSX
  );
}
```

## Privacy and GDPR Compliance

### Cookie Consent

The integration includes a comprehensive cookie consent system:

- **Granular Controls**: Users can choose which types of cookies to accept
- **Consent Mode**: Google Analytics Consent Mode for privacy-compliant tracking
- **Persistent Storage**: Consent preferences are stored locally
- **Version Management**: Consent is re-requested when privacy policy updates

### Privacy Features

- **Do Not Track**: Respects browser DNT header
- **IP Anonymization**: Automatically anonymizes IP addresses
- **Data Retention**: Configurable data retention periods
- **Consent Mode**: Limited data collection when consent is denied
- **Cookie Domain**: Configurable cookie domain settings

### Consent Categories

1. **Necessary Cookies**: Always enabled, required for basic functionality
2. **Functional Cookies**: Remember user preferences and settings
3. **Analytics Cookies**: Collect anonymous usage data for improvements
4. **Marketing Cookies**: Personalize ads and content

## Testing

### Running Tests

```bash
# Run all analytics tests
npm test -- analytics

# Run specific test files
npm test -- GoogleAnalyticsManager.test.ts
npm test -- CookieConsent.test.tsx
npm test -- useAnalyticsTracking.test.ts
```

### Test Coverage

The analytics implementation includes comprehensive tests for:

- ✅ Analytics manager initialization and configuration
- ✅ Event tracking functionality
- ✅ Consent management
- ✅ Cookie consent component
- ✅ Analytics hooks
- ✅ Error handling and edge cases

### Manual Testing

1. **Enable Debug Mode**: Set `GOOGLE_ANALYTICS_DEBUG=true` in `.env`
2. **Check Browser Console**: Look for `[Analytics]` debug messages
3. **Verify GA4 Events**: Use Google Analytics DebugView
4. **Test Consent Flow**: Clear localStorage and test consent banner
5. **Test Do Not Track**: Enable DNT in browser settings

## Troubleshooting

### Common Issues

#### Analytics Not Loading

1. Check if `GOOGLE_ANALYTICS_ENABLED=true` in `.env`
2. Verify `GOOGLE_ANALYTICS_MEASUREMENT_ID` is set correctly
3. Check browser console for script loading errors
4. Ensure ad blockers are not blocking analytics scripts

#### Events Not Tracking

1. Verify analytics is initialized: `analytics.isInitialized()`
2. Check consent status: ensure analytics consent is granted
3. Enable debug mode to see tracking attempts
4. Verify measurement ID format: `G-XXXXXXXXXX`

#### Consent Banner Not Showing

1. Clear localStorage: `localStorage.removeItem('cookie_consent_preferences')`
2. Check if `ANALYTICS_COOKIE_CONSENT_REQUIRED=true`
3. Verify component is properly imported and rendered

#### Do Not Track Issues

1. Check browser DNT setting: `navigator.doNotTrack`
2. Verify `ANALYTICS_RESPECT_DNT=true` in configuration
3. Test with DNT disabled to confirm tracking works

### Debug Mode

Enable debug mode for detailed logging:

```env
GOOGLE_ANALYTICS_DEBUG=true
```

This will:
- Log all analytics events to browser console
- Enable gtag debug mode
- Show detailed initialization steps
- Log consent changes and user property updates

### Support

For additional support:

1. Check the [Google Analytics 4 documentation](https://developers.google.com/analytics/devguides/collection/ga4)
2. Review the test files for usage examples
3. Enable debug mode for detailed logging
4. Check browser developer tools for network requests and console errors

## Additional Resources

- [Usage Examples](./examples.md) - Detailed code examples for common use cases
- [API Reference](./api-reference.md) - Complete API documentation
- [Migration Guide](./migration.md) - Migrating from Universal Analytics (GA3)
