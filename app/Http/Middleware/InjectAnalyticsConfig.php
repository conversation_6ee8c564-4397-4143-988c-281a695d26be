<?php

namespace App\Http\Middleware;

use App\Services\GoogleAnalyticsService;
use Closure;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

class InjectAnalyticsConfig
{
    /**
     * Create a new middleware instance.
     */
    public function __construct(
        private GoogleAnalyticsService $analyticsService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Share analytics configuration with all Inertia responses
        Inertia::share([
            'analytics' => fn () => $this->analyticsService->getConfig(),
        ]);

        return $next($request);
    }
}
