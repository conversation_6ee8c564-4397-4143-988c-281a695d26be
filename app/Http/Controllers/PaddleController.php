<?php

namespace App\Http\Controllers;

use App\Models\PaddleTransaction;
use App\Models\PaddleWebhook;
use App\Models\PricingPlan;
use App\Services\CsrfTokenService;
use App\Services\PaddleService;
use App\Services\SubscriptionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class PaddleController extends Controller
{
    public function __construct(
        private PaddleService $paddleService,
        private SubscriptionService $subscriptionService,
        private CsrfTokenService $csrfTokenService
    ) {}

    /**
     * Get Paddle configuration for frontend.
     */
    public function config(): JsonResponse
    {
        if (!$this->paddleService->isConfigured()) {
            $errorMessage = 'Paddle is not configured';
            $statusCode = 503;

            // Provide more helpful error message in development mode
            if ($this->paddleService->isDevelopmentMode()) {
                $errorMessage = 'Paddle is using placeholder credentials. Please configure real Paddle sandbox credentials for testing.';
                $statusCode = 200; // Return 200 in dev mode to allow frontend to handle gracefully

                return response()->json([
                    'error' => $errorMessage,
                    'development_mode' => true,
                    'help' => 'Set PADDLE_API_KEY and PADDLE_CLIENT_TOKEN in your .env file with real Paddle sandbox credentials.'
                ], $statusCode);
            }

            return response()->json([
                'error' => $errorMessage
            ], $statusCode);
        }

        return response()->json($this->paddleService->getFrontendConfig());
    }

    /**
     * Create a checkout session.
     */
    public function createCheckout(Request $request): JsonResponse
    {
        // Enhanced CSRF and session debugging
        if (config('app.debug')) {
            $csrfValidation = $this->csrfTokenService->validateToken($request);
            $this->csrfTokenService->logValidationAttempt($request, $csrfValidation);

            Log::info('Paddle checkout request received', [
                'user_id' => $request->user()?->id,
                'request_data' => $request->all(),
                'csrf_validation' => $csrfValidation,
                'session_debug' => $this->csrfTokenService->getSessionDebugInfo($request),
            ]);
        }

        $user = $request->user();

        if ($user->isPremium()) {
            return response()->json([
                'error' => 'You already have an active premium subscription.'
            ], 400);
        }

        $validated = $request->validate([
            'plan_id' => 'required|exists:pricing_plans,id',
            'billing_cycle' => 'required|in:month,year',
        ]);

        $plan = PricingPlan::findOrFail($validated['plan_id']);

        if (!$plan->is_active) {
            return response()->json([
                'error' => 'This plan is not available.'
            ], 404);
        }

        if (!$plan->hasPaddleIntegration()) {
            return response()->json([
                'error' => 'This plan does not support online payments.'
            ], 400);
        }

        if (!$plan->supportsBillingCycle($validated['billing_cycle'])) {
            return response()->json([
                'error' => "This plan does not support {$validated['billing_cycle']}ly billing."
            ], 400);
        }

        // Enhanced debugging for development environment
        if (config('app.debug')) {
            Log::info('Paddle checkout attempt', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'billing_cycle' => $validated['billing_cycle'],
                'price_id_monthly' => $plan->paddle_price_id_monthly,
                'price_id_yearly' => $plan->paddle_price_id_yearly,
                'is_development_mode' => $this->paddleService->isDevelopmentMode(),
                'is_configured' => $this->paddleService->isConfigured(),
                'paddle_environment' => config('paddle.environment'),
                'has_api_key' => !empty(config('paddle.api_key')),
                'has_client_token' => !empty(config('paddle.client_token')),
            ]);
        }

        $checkoutData = $this->paddleService->createCheckoutSession(
            $user,
            $plan,
            $validated['billing_cycle']
        );

        if (!$checkoutData) {
            $errorMessage = 'Failed to create checkout session. Please try again.';

            // Enhanced debugging for null response
            if (config('app.debug')) {
                Log::error('Paddle checkout session creation returned null', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'billing_cycle' => $validated['billing_cycle'],
                    'is_development_mode' => $this->paddleService->isDevelopmentMode(),
                    'is_configured' => $this->paddleService->isConfigured(),
                ]);
            }

            // Provide more helpful error message in development mode
            if ($this->paddleService->isDevelopmentMode()) {
                $errorMessage = 'Paddle checkout failed: Using placeholder credentials. Please configure real Paddle sandbox credentials for testing.';

                return response()->json([
                    'error' => $errorMessage,
                    'development_mode' => true,
                    'help' => 'Set PADDLE_API_KEY and PADDLE_CLIENT_TOKEN in your .env file with real Paddle sandbox credentials.',
                    'documentation' => 'Visit https://developer.paddle.com/getting-started for setup instructions.'
                ], 400);
            }

            return response()->json([
                'error' => $errorMessage
            ], 500);
        }

        // Check if this is an error response from the service
        if (isset($checkoutData['error'])) {
            // Enhanced debugging for service error responses
            if (config('app.debug')) {
                Log::error('Paddle service returned error response', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'billing_cycle' => $validated['billing_cycle'],
                    'error_type' => $checkoutData['error'],
                    'error_message' => $checkoutData['message'] ?? 'No message provided',
                    'full_response' => $checkoutData,
                ]);
            }

            // Handle placeholder price ID error specifically
            if ($checkoutData['error'] === 'placeholder_price_id') {
                return response()->json([
                    'error' => "The pricing plan '{$plan->display_name}' is using placeholder price IDs that haven't been configured with real Paddle price IDs yet.",
                    'development_mode' => true,
                    'help' => 'Please ensure the price IDs in your pricing plan are valid Paddle price IDs from your Paddle dashboard.',
                    'price_id' => $checkoutData['price_id'] ?? null,
                    'plan_name' => $plan->display_name,
                ], 400);
            }

            return response()->json([
                'error' => $checkoutData['message'] ?? $checkoutData['error'],
                'development_mode' => $checkoutData['development_mode'] ?? false,
                'help' => $checkoutData['help'] ?? 'Please check your configuration.',
                'documentation' => 'Visit https://developer.paddle.com/catalog/products for setup instructions.',
                'current_price_id' => $checkoutData['price_id'] ?? null,
                'plan_name' => $checkoutData['plan_name'] ?? $plan->name
            ], 400);
        }

        // Enhanced debugging for successful checkout session creation
        if (config('app.debug')) {
            Log::info('Paddle checkout session created successfully', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'billing_cycle' => $validated['billing_cycle'],
                'transaction_id' => $checkoutData['transaction_id'],
                'checkout_url' => $checkoutData['checkout_url'] ?? 'Not provided',
                'development_mode' => $checkoutData['development_mode'] ?? false,
                'price_id' => $checkoutData['price_id'] ?? 'Not provided',
            ]);
        }

        // Store transaction record
        PaddleTransaction::create([
            'paddle_transaction_id' => $checkoutData['transaction_id'],
            'user_id' => $user->id,
            'paddle_customer_id' => $checkoutData['customer_id'] ?? null,
            'status' => 'pending',
            'currency' => $plan->currency,
            'amount' => $plan->price,
            'total_amount' => $plan->price,
            'items' => [
                [
                    'price_id' => $checkoutData['price_id'] ?? 'Not provided',
                    'quantity' => 1,
                    'plan_name' => $plan->name,
                    'billing_cycle' => $validated['billing_cycle'],
                ]
            ],
            'checkout_details' => [
                'checkout_url' => $checkoutData['checkout_url'],
                'billing_cycle' => $validated['billing_cycle'],
            ],
        ]);

        // Check if the checkout URL is pointing to our success page (indicating dashboard misconfiguration)
        $checkoutUrl = $checkoutData['checkout_url'];
        $successUrl = route('paddle.success');

        if (str_contains($checkoutUrl, '/paddle/success')) {
            // Dashboard is misconfigured, redirect to our custom checkout page instead
            $checkoutUrl = route('paddle.checkout.page') . '?_ptxn=' . $checkoutData['transaction_id'];

            if (config('app.debug')) {
                Log::info('Paddle dashboard misconfiguration detected, using custom checkout page', [
                    'original_checkout_url' => $checkoutData['checkout_url'],
                    'custom_checkout_url' => $checkoutUrl,
                    'transaction_id' => $checkoutData['transaction_id'],
                ]);
            }
        }

        return response()->json([
            'checkout_url' => $checkoutUrl,
            'transaction_id' => $checkoutData['transaction_id'],
        ]);
    }

    /**
     * Mock checkout page for development mode.
     */
    public function mockCheckout(Request $request)
    {
        if (!config('app.debug')) {
            abort(404);
        }

        $transactionId = $request->query('transaction');

        return response()->view('paddle.mock-checkout', [
            'transaction_id' => $transactionId,
            'message' => 'This is a mock Paddle checkout page for development mode.',
            'help' => 'Configure real Paddle sandbox credentials to test actual payments.',
        ]);
    }

    /**
     * Development status endpoint for debugging.
     */
    public function developmentStatus(Request $request): JsonResponse
    {
        if (!config('app.debug')) {
            abort(404);
        }

        $config = config('paddle');

        return response()->json([
            'paddle_status' => [
                'is_configured' => $this->paddleService->isConfigured(),
                'is_development_mode' => $this->paddleService->isDevelopmentMode(),
                'environment' => $config['environment'] ?? 'not_set',
                'credentials' => [
                    'api_key_present' => !empty($config['api_key']),
                    'api_key_is_placeholder' => !empty($config['api_key']) ? $this->isPlaceholderValue($config['api_key']) : false,
                    'client_token_present' => !empty($config['client_token']),
                    'client_token_is_placeholder' => !empty($config['client_token']) ? $this->isPlaceholderValue($config['client_token']) : false,
                    'webhook_secret_present' => !empty($config['webhook_secret']),
                    'webhook_secret_is_placeholder' => !empty($config['webhook_secret']) ? $this->isPlaceholderValue($config['webhook_secret']) : false,
                ],
            ],
            'recommendations' => $this->getConfigurationRecommendations(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Check if a value is a placeholder.
     */
    private function isPlaceholderValue(string $value): bool
    {
        $placeholderPatterns = [
            'test_',
            'placeholder',
            'demo_',
            'example_',
            'your_',
            'replace_',
        ];

        $lowerValue = strtolower($value);

        foreach ($placeholderPatterns as $pattern) {
            if (str_contains($lowerValue, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get configuration recommendations.
     */
    private function getConfigurationRecommendations(): array
    {
        $recommendations = [];

        if ($this->paddleService->isDevelopmentMode()) {
            $recommendations[] = 'Configure real Paddle sandbox credentials for testing';
            $recommendations[] = 'Visit https://vendors.paddle.com to get your API credentials';
            $recommendations[] = 'Update PADDLE_API_KEY and PADDLE_CLIENT_TOKEN in your .env file';
        }

        if (!$this->paddleService->isConfigured() && !$this->paddleService->isDevelopmentMode()) {
            $recommendations[] = 'Paddle credentials are missing or invalid';
            $recommendations[] = 'Check your .env file configuration';
        }

        return $recommendations;
    }

    /**
     * Handle Paddle webhooks.
     */
    public function webhook(Request $request): Response
    {
        $payload = $request->getContent();
        $signature = $request->header('Paddle-Signature');

        // Verify webhook signature
        if (!$this->paddleService->verifyWebhookSignature($payload, $signature)) {
            Log::warning('Invalid Paddle webhook signature', [
                'signature' => $signature,
                'payload_length' => strlen($payload)
            ]);
            return response('Invalid signature', 400);
        }

        $data = json_decode($payload, true);

        if (!$data || !isset($data['event_type'])) {
            Log::warning('Invalid Paddle webhook payload', ['payload' => $payload]);
            return response('Invalid payload', 400);
        }

        // Store webhook for processing
        $webhook = PaddleWebhook::create([
            'paddle_event_id' => $data['event_id'] ?? uniqid('paddle_'),
            'event_type' => $data['event_type'],
            'status' => 'pending',
            'payload' => $data,
            'signature' => $signature,
            'paddle_occurred_at' => isset($data['occurred_at'])
                ? \Carbon\Carbon::parse($data['occurred_at'])
                : now(),
        ]);

        // Process webhook asynchronously
        try {
            $this->processWebhook($webhook);
        } catch (\Exception $e) {
            Log::error('Failed to process Paddle webhook', [
                'webhook_id' => $webhook->id,
                'event_type' => $webhook->event_type,
                'error' => $e->getMessage()
            ]);
            $webhook->markAsFailed($e->getMessage());
        }

        return response('OK', 200);
    }

    /**
     * Process a Paddle webhook.
     */
    protected function processWebhook(PaddleWebhook $webhook): void
    {
        $data = $webhook->getData();

        switch ($webhook->event_type) {
            case 'transaction.completed':
                $this->handleTransactionCompleted($webhook, $data);
                break;

            case 'transaction.payment_failed':
                $this->handleTransactionFailed($webhook, $data);
                break;

            case 'subscription.created':
                $this->handleSubscriptionCreated($webhook, $data);
                break;

            case 'subscription.updated':
                $this->handleSubscriptionUpdated($webhook, $data);
                break;

            case 'subscription.cancelled':
                $this->handleSubscriptionCancelled($webhook, $data);
                break;

            default:
                Log::info('Unhandled Paddle webhook event', [
                    'event_type' => $webhook->event_type,
                    'webhook_id' => $webhook->id
                ]);
        }

        $webhook->markAsProcessed();
    }

    /**
     * Handle transaction completed webhook.
     */
    protected function handleTransactionCompleted(PaddleWebhook $webhook, array $data): void
    {
        $transactionId = $data['id'] ?? null;
        if (!$transactionId) {
            throw new \Exception('Transaction ID not found in webhook data');
        }

        $transaction = PaddleTransaction::where('paddle_transaction_id', $transactionId)->first();
        if (!$transaction) {
            Log::warning('Paddle transaction not found', ['transaction_id' => $transactionId]);
            return;
        }

        DB::transaction(function () use ($transaction, $data) {
            // Update transaction status
            $transaction->update([
                'status' => 'completed',
                'paddle_data' => $data,
                'paddle_updated_at' => now(),
            ]);

            // Create or update subscription
            $this->subscriptionService->createPaddleSubscription(
                $transaction->user,
                $transaction,
                $data
            );

            Log::info('Paddle transaction completed', [
                'transaction_id' => $transaction->paddle_transaction_id,
                'user_id' => $transaction->user_id
            ]);
        });
    }

    /**
     * Handle transaction failed webhook.
     */
    protected function handleTransactionFailed(PaddleWebhook $webhook, array $data): void
    {
        $transactionId = $data['id'] ?? null;
        if (!$transactionId) {
            throw new \Exception('Transaction ID not found in webhook data');
        }

        $transaction = PaddleTransaction::where('paddle_transaction_id', $transactionId)->first();
        if ($transaction) {
            $transaction->update([
                'status' => 'failed',
                'paddle_data' => $data,
                'paddle_updated_at' => now(),
            ]);

            Log::info('Paddle transaction failed', [
                'transaction_id' => $transaction->paddle_transaction_id,
                'user_id' => $transaction->user_id
            ]);
        }
    }

    /**
     * Handle subscription created webhook.
     */
    protected function handleSubscriptionCreated(PaddleWebhook $webhook, array $data): void
    {
        $subscriptionId = $data['id'] ?? null;
        if (!$subscriptionId) {
            throw new \Exception('Subscription ID not found in webhook data');
        }

        // Find user by customer ID
        $customerId = $data['customer_id'] ?? null;
        if (!$customerId) {
            throw new \Exception('Customer ID not found in webhook data');
        }

        $user = \App\Models\User::where('paddle_customer_id', $customerId)->first();
        if (!$user) {
            Log::warning('User not found for Paddle customer', ['customer_id' => $customerId]);
            return;
        }

        // Update existing subscription with Paddle subscription ID
        $subscription = $user->activeSubscription;
        if ($subscription) {
            $subscription->update([
                'paddle_subscription_id' => $subscriptionId,
            ]);

            Log::info('Updated subscription with Paddle subscription ID', [
                'subscription_id' => $subscription->id,
                'paddle_subscription_id' => $subscriptionId,
                'user_id' => $user->id
            ]);
        }
    }

    /**
     * Handle subscription updated webhook.
     */
    protected function handleSubscriptionUpdated(PaddleWebhook $webhook, array $data): void
    {
        $subscriptionId = $data['id'] ?? null;
        if (!$subscriptionId) {
            throw new \Exception('Subscription ID not found in webhook data');
        }

        $subscription = \App\Models\Subscription::where('paddle_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            // Update subscription details based on Paddle data
            // This could include plan changes, billing cycle changes, etc.
            Log::info('Paddle subscription updated', [
                'subscription_id' => $subscription->id,
                'paddle_subscription_id' => $subscriptionId
            ]);
        }
    }

    /**
     * Handle subscription cancelled webhook.
     */
    protected function handleSubscriptionCancelled(PaddleWebhook $webhook, array $data): void
    {
        $subscriptionId = $data['id'] ?? null;
        if (!$subscriptionId) {
            throw new \Exception('Subscription ID not found in webhook data');
        }

        $subscription = \App\Models\Subscription::where('paddle_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            $subscription->update(['status' => 'cancelled']);

            Log::info('Paddle subscription cancelled', [
                'subscription_id' => $subscription->id,
                'paddle_subscription_id' => $subscriptionId,
                'user_id' => $subscription->user_id
            ]);
        }
    }

    /**
     * Show Paddle checkout page for transaction.
     */
    public function checkout(Request $request)
    {
        $transactionId = $request->get('_ptxn');

        Log::info('Paddle checkout page accessed', [
            'transaction_id' => $transactionId,
            'all_params' => $request->all(),
            'url' => $request->fullUrl()
        ]);

        // If no transaction ID, redirect to subscription checkout
        if (!$transactionId) {
            Log::warning('No transaction ID provided to Paddle checkout page');
            return redirect()->route('subscription.checkout')
                ->with('error', 'No transaction ID provided.');
        }

        // Validate transaction exists and is valid (development debugging)
        // Skip validation for development mode transactions (dev_txn_ prefix)
        if (config('app.debug') && !str_starts_with($transactionId, 'dev_txn_')) {
            try {
                $transaction = $this->paddleService->getTransaction($transactionId);
                Log::info('Transaction validation for checkout', [
                    'transaction_id' => $transactionId,
                    'transaction_exists' => !!$transaction,
                    'transaction_status' => $transaction?->status ?? 'not_found',
                    'transaction_data' => $transaction ? [
                        'id' => $transaction->id,
                        'status' => $transaction->status,
                        'customer_id' => $transaction->customerId ?? null,
                    ] : null
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to validate transaction for checkout', [
                    'transaction_id' => $transactionId,
                    'error' => $e->getMessage()
                ]);
            }
        } elseif (str_starts_with($transactionId, 'dev_txn_')) {
            Log::info('Skipping transaction validation for development mode transaction', [
                'transaction_id' => $transactionId,
                'is_development_mode' => true
            ]);
        }

        return Inertia::render('paddle/Checkout', [
            'transaction_id' => $transactionId
        ]);
    }

    /**
     * Show checkout success page.
     */
    public function success(Request $request)
    {
        $transactionId = $request->get('_ptxn') ?? $request->get('transaction_id');

        // Redirect to unified success page
        return redirect()->route('subscription.success', [
            'transaction_id' => $transactionId,
            'gateway' => 'paddle'
        ])->with('success', 'Payment completed successfully!');
    }

    /**
     * Show checkout cancelled page.
     */
    public function cancelled(Request $request)
    {
        $transactionId = $request->get('_ptxn') ?? $request->get('transaction_id');
        $reason = $request->get('reason', 'Payment was cancelled');

        // Redirect to unified cancelled page
        return redirect()->route('subscription.cancelled', [
            'reason' => $reason,
            'gateway' => 'paddle'
        ])->with('info', 'Payment was cancelled.');
    }

}
