<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        // Check if this is an admin email
        $adminEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        $isAdmin = in_array($request->email, $adminEmails);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'status' => 'pending', // User starts as pending until email verification
            'approval_status' => 'approved', // Auto-approved for self-registration
            'is_admin' => $isAdmin,
            'role' => $isAdmin ? 'admin' : 'user',
            'subscription_plan' => $isAdmin ? 'premium' : 'free', // Explicitly set subscription plan
            'search_count' => 0, // Initialize search count
            'daily_reset' => now()->toDateString(), // Set initial daily reset
        ]);

        // Fire the Registered event to send email verification notification
        event(new Registered($user));

        // Log the user in so they can access the verification page
        Auth::login($user);

        // Redirect to email verification prompt instead of dashboard
        return redirect()->route('verification.notice')
            ->with('status', 'registration-successful');
    }
}
