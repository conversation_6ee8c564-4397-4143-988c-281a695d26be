<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\GoogleAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class GoogleAnalyticsConfigController extends Controller
{
    public function __construct(
        private GoogleAnalyticsService $analyticsService
    ) {
        //
    }

    /**
     * Display Google Analytics configuration dashboard.
     */
    public function index(): Response
    {
        try {
            $currentConfig = [
                'enabled' => config('analytics.google.enabled', false),
                'measurement_id' => config('analytics.google.measurement_id'),
                'debug' => config('analytics.google.debug', false),
                'cookie_domain' => config('analytics.google.cookie_domain', 'auto'),
                'cookie_expires' => config('analytics.google.cookie_expires', 63072000),
                'anonymize_ip' => config('analytics.google.anonymize_ip', true),
                'consent_mode' => config('analytics.google.consent_mode.enabled', true),
                'cookie_consent_required' => config('analytics.privacy.cookie_consent_required', true),
                'respect_dnt' => config('analytics.privacy.respect_dnt', true),
                'data_retention_days' => config('analytics.privacy.data_retention_days', 730),
            ];

            $status = $this->analyticsService->getStatus();
            $configValidation = $this->validateConfiguration($currentConfig);

            return Inertia::render('admin/GoogleAnalytics/Index', [
                'config' => $currentConfig,
                'status' => $status,
                'validation' => $configValidation,
                'error_details' => session('error_details'),
            ]);
        } catch (\Exception $e) {
            Log::error('Google Analytics configuration page error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // Return with fallback data to prevent crashes
            $fallbackConfig = [
                'enabled' => false,
                'measurement_id' => '',
                'debug' => false,
                'cookie_domain' => 'auto',
                'cookie_expires' => 63072000,
                'anonymize_ip' => true,
                'consent_mode' => true,
                'cookie_consent_required' => true,
                'respect_dnt' => true,
                'data_retention_days' => 730,
            ];

            $fallbackStatus = [
                'configured' => false,
                'enabled' => false,
                'measurement_id_valid' => false,
                'status' => 'error'
            ];

            return Inertia::render('admin/GoogleAnalytics/Index', [
                'config' => $fallbackConfig,
                'status' => $fallbackStatus,
                'validation' => ['valid' => false, 'errors' => ['Configuration error occurred']],
                'error_details' => [
                    'message' => 'Failed to load Google Analytics configuration',
                    'details' => $e->getMessage()
                ],
            ]);
        }
    }

    /**
     * Update Google Analytics configuration.
     */
    public function updateConfig(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
            'measurement_id' => 'required_if:enabled,true|nullable|string|regex:/^G-[A-Z0-9]{10}$/',
            'debug' => 'boolean',
            'cookie_domain' => 'nullable|string',
            'cookie_expires' => 'nullable|integer|min:0',
            'anonymize_ip' => 'boolean',
            'consent_mode' => 'boolean',
            'cookie_consent_required' => 'boolean',
            'respect_dnt' => 'boolean',
            'data_retention_days' => 'nullable|integer|min:1|max:1460',
        ]);

        try {
            // Update environment variables
            $envUpdates = [
                'GOOGLE_ANALYTICS_ENABLED' => $validated['enabled'] ? 'true' : 'false',
                'GOOGLE_ANALYTICS_MEASUREMENT_ID' => $validated['measurement_id'] ?? '',
                'GOOGLE_ANALYTICS_DEBUG' => ($validated['debug'] ?? false) ? 'true' : 'false',
            ];

            // Optional advanced configuration
            if (isset($validated['cookie_domain'])) {
                $envUpdates['GOOGLE_ANALYTICS_COOKIE_DOMAIN'] = $validated['cookie_domain'];
            }
            if (isset($validated['cookie_expires'])) {
                $envUpdates['GOOGLE_ANALYTICS_COOKIE_EXPIRES'] = $validated['cookie_expires'];
            }
            if (isset($validated['anonymize_ip'])) {
                $envUpdates['GOOGLE_ANALYTICS_ANONYMIZE_IP'] = $validated['anonymize_ip'] ? 'true' : 'false';
            }
            if (isset($validated['consent_mode'])) {
                $envUpdates['GOOGLE_ANALYTICS_CONSENT_MODE'] = $validated['consent_mode'] ? 'true' : 'false';
            }

            // Privacy settings
            if (isset($validated['cookie_consent_required'])) {
                $envUpdates['ANALYTICS_COOKIE_CONSENT_REQUIRED'] = $validated['cookie_consent_required'] ? 'true' : 'false';
            }
            if (isset($validated['respect_dnt'])) {
                $envUpdates['ANALYTICS_RESPECT_DNT'] = $validated['respect_dnt'] ? 'true' : 'false';
            }
            if (isset($validated['data_retention_days'])) {
                $envUpdates['ANALYTICS_DATA_RETENTION_DAYS'] = $validated['data_retention_days'];
            }

            $this->updateEnvFile($envUpdates);

            return redirect()->back()
                ->with('success', 'Google Analytics configuration updated successfully.');
        } catch (\Exception $e) {
            Log::error('Google Analytics configuration update failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to update Google Analytics configuration: ' . $e->getMessage());
        }
    }

    /**
     * Test Google Analytics configuration.
     */
    public function testConfig(Request $request): RedirectResponse
    {
        try {
            $result = $this->analyticsService->testConfiguration();

            if ($result['success']) {
                return redirect()->back()
                    ->with('success', $result['message']);
            } else {
                $errorData = [
                    'message' => $result['message'],
                    'troubleshooting' => $result['troubleshooting'] ?? null,
                    'details' => $result['details'] ?? null
                ];

                return redirect()->back()
                    ->with('error', $result['message'])
                    ->with('error_details', $errorData);
            }
        } catch (\Exception $e) {
            Log::error('Google Analytics configuration test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to test Google Analytics configuration: ' . $e->getMessage());
        }
    }

    /**
     * Get Google Analytics documentation.
     */
    public function getDocumentation()
    {
        $docs = [
            'title' => 'Google Analytics 4 Configuration',
            'description' => 'Configure Google Analytics 4 for comprehensive website analytics with GDPR compliance.',
            'setup_steps' => [
                '1. Create a Google Analytics account at https://analytics.google.com',
                '2. Create a new GA4 property for your website',
                '3. Copy the Measurement ID (format: G-XXXXXXXXXX)',
                '4. Paste the Measurement ID in the configuration below',
                '5. Configure privacy settings according to your requirements',
                '6. Test the configuration to ensure it\'s working correctly',
            ],
            'features' => [
                'Real-time visitor tracking',
                'Page view and event analytics',
                'User behavior insights',
                'Conversion tracking',
                'GDPR-compliant cookie consent',
                'Do Not Track header respect',
                'IP anonymization',
                'Configurable data retention',
            ],
            'privacy_compliance' => [
                'Cookie consent management',
                'IP address anonymization',
                'Do Not Track header support',
                'Configurable data retention periods',
                'Consent mode for enhanced privacy',
                'User opt-out capabilities',
            ],
        ];

        return response()->json($docs);
    }

    /**
     * Validate Google Analytics configuration.
     */
    private function validateConfiguration(array $config): array
    {
        $errors = [];
        $warnings = [];

        // Check if enabled but no measurement ID
        if ($config['enabled'] && empty($config['measurement_id'])) {
            $errors[] = 'Measurement ID is required when Google Analytics is enabled';
        }

        // Validate measurement ID format
        if (!empty($config['measurement_id']) && !preg_match('/^G-[A-Z0-9]{10}$/', $config['measurement_id'])) {
            $errors[] = 'Invalid Measurement ID format. Should be G-XXXXXXXXXX';
        }

        // Check privacy settings
        if ($config['enabled'] && !$config['cookie_consent_required']) {
            $warnings[] = 'Consider enabling cookie consent for GDPR compliance';
        }

        if ($config['enabled'] && !$config['anonymize_ip']) {
            $warnings[] = 'Consider enabling IP anonymization for enhanced privacy';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
        ];
    }

    /**
     * Update environment file.
     */
    private function updateEnvFile(array $data): void
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}=" . $this->formatEnvValue($value);

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }

    /**
     * Format environment variable value.
     */
    private function formatEnvValue($value): string
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_null($value)) {
            return '';
        }

        $value = (string) $value;

        // Quote values that contain spaces or special characters
        if (preg_match('/\s|[#"\'\\\\]/', $value)) {
            return '"' . str_replace('"', '\\"', $value) . '"';
        }

        return $value;
    }
}
