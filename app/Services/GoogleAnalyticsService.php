<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class GoogleAnalyticsService
{
    /**
     * Check if Google Analytics is enabled.
     */
    public function isEnabled(): bool
    {
        return Config::get('analytics.google.enabled', false) && 
               !empty(Config::get('analytics.google.measurement_id'));
    }

    /**
     * Get the Google Analytics measurement ID.
     */
    public function getMeasurementId(): ?string
    {
        return Config::get('analytics.google.measurement_id');
    }

    /**
     * Check if debug mode is enabled.
     */
    public function isDebugMode(): bool
    {
        return Config::get('analytics.google.debug', false);
    }

    /**
     * Get the analytics configuration for frontend.
     */
    public function getConfig(): array
    {
        if (!$this->isEnabled()) {
            return [
                'enabled' => false,
            ];
        }

        return [
            'enabled' => true,
            'measurement_id' => $this->getMeasurementId(),
            'debug' => $this->isDebugMode(),
            'cookie_domain' => Config::get('analytics.google.cookie_domain', 'auto'),
            'cookie_expires' => Config::get('analytics.google.cookie_expires', 63072000),
            'anonymize_ip' => Config::get('analytics.google.anonymize_ip', true),
            'consent_mode' => Config::get('analytics.google.consent_mode', []),
            'auto_track' => Config::get('analytics.google.auto_track', []),
            'privacy' => Config::get('analytics.privacy', []),
        ];
    }

    /**
     * Check if the user should be tracked based on privacy settings.
     */
    public function shouldTrackUser(): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        // Check Do Not Track header
        if (Config::get('analytics.privacy.respect_do_not_track', true)) {
            $dnt = request()->header('DNT') ?? request()->header('dnt');
            if ($dnt === '1') {
                return false;
            }
        }

        return true;
    }

    /**
     * Log analytics events for debugging.
     */
    public function logEvent(string $event, array $data = []): void
    {
        if ($this->isDebugMode()) {
            Log::channel('daily')->info('Google Analytics Event', [
                'event' => $event,
                'data' => $data,
                'user_agent' => request()->userAgent(),
                'ip' => request()->ip(),
                'timestamp' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Get custom dimensions configuration.
     */
    public function getCustomDimensions(): array
    {
        return Config::get('analytics.google.custom_dimensions', []);
    }

    /**
     * Check if consent is required for tracking.
     */
    public function isConsentRequired(): bool
    {
        return Config::get('analytics.privacy.cookie_consent_required', true);
    }

    /**
     * Get the consent mode configuration.
     */
    public function getConsentModeConfig(): array
    {
        return Config::get('analytics.google.consent_mode', [
            'enabled' => true,
            'default_settings' => [
                'ad_storage' => 'denied',
                'analytics_storage' => 'denied',
                'ad_user_data' => 'denied',
                'ad_personalization' => 'denied',
                'functionality_storage' => 'granted',
                'security_storage' => 'granted',
            ],
        ]);
    }

    /**
     * Get the current status of Google Analytics configuration.
     */
    public function getStatus(): array
    {
        $measurementId = $this->getMeasurementId();
        $isValidMeasurementId = !empty($measurementId) && preg_match('/^G-[A-Z0-9]{10}$/', $measurementId);

        return [
            'configured' => !empty($measurementId),
            'enabled' => $this->isEnabled(),
            'measurement_id_valid' => $isValidMeasurementId,
            'status' => $this->isEnabled() && $isValidMeasurementId ? 'active' : 'inactive',
        ];
    }

    /**
     * Test the Google Analytics configuration.
     */
    public function testConfiguration(): array
    {
        try {
            $measurementId = $this->getMeasurementId();

            // Check if measurement ID is provided
            if (empty($measurementId)) {
                return [
                    'success' => false,
                    'message' => 'Measurement ID is required.',
                    'troubleshooting' => 'Please provide a valid Google Analytics 4 Measurement ID.',
                    'details' => 'The Measurement ID should be in the format G-XXXXXXXXXX'
                ];
            }

            // Validate measurement ID format
            if (!preg_match('/^G-[A-Z0-9]{10}$/', $measurementId)) {
                return [
                    'success' => false,
                    'message' => 'Invalid Measurement ID format.',
                    'troubleshooting' => 'Please check your Measurement ID format.',
                    'details' => 'The Measurement ID should be in the format G-XXXXXXXXXX'
                ];
            }

            // Check if analytics is enabled
            if (!$this->isEnabled()) {
                return [
                    'success' => false,
                    'message' => 'Google Analytics is disabled.',
                    'troubleshooting' => 'Please enable Google Analytics in the configuration.',
                    'details' => 'Set GOOGLE_ANALYTICS_ENABLED=true in your environment configuration'
                ];
            }

            // All checks passed
            return [
                'success' => true,
                'message' => 'Google Analytics configuration is valid and working correctly.',
                'details' => [
                    'measurement_id' => $measurementId,
                    'debug_mode' => $this->isDebugMode(),
                    'consent_required' => $this->isConsentRequired(),
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Google Analytics configuration test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Configuration test failed: ' . $e->getMessage(),
                'troubleshooting' => 'Please check your configuration and try again.',
                'details' => $e->getMessage()
            ];
        }
    }
}
