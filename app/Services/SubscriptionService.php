<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\DB;

class SubscriptionService
{
    /**
     * Check if user can perform a search.
     */
    public function canUserSearch(User $user): bool
    {
        // Admin users have unlimited search access
        if ($user->isAdmin()) {
            return true;
        }

        // Get user's search limit from their subscription plan
        $searchLimit = $this->getUserSearchLimit($user);

        // Unlimited searches (-1)
        if ($searchLimit === -1) {
            return true;
        }

        // Reset daily count if needed
        if (!$user->daily_reset || $user->daily_reset->lt(now()->startOfDay())) {
            $user->update([
                'search_count' => 0,
                'daily_reset' => now()->toDateString(),
            ]);
        }

        return $user->search_count < $searchLimit;
    }

    /**
     * Record a search for the user.
     */
    public function recordSearch(User $user, string $query, string $type, int $resultsCount = 0): void
    {
        DB::transaction(function () use ($user, $query, $type, $resultsCount) {
            // Record the search
            $user->searches()->create([
                'search_query' => $query,
                'search_type' => $type,
                'results_count' => $resultsCount,
                'created_at' => now(),
            ]);

            // Increment search count for tracking purposes (except admin and premium users)
            // Premium users have unlimited searches, so no need to track count for limiting
            if (!$user->isAdmin() && !$user->isPremium()) {
                $user->increment('search_count');
            }
        });
    }

    /**
     * Get user's remaining searches for today.
     */
    public function getRemainingSearches(User $user): int
    {
        // Admin users have unlimited searches
        if ($user->isAdmin()) {
            return -1; // Unlimited
        }

        // Get user's search limit from their subscription plan
        $searchLimit = $this->getUserSearchLimit($user);

        // Unlimited searches (-1)
        if ($searchLimit === -1) {
            return -1;
        }

        // Reset daily count if needed
        if (!$user->daily_reset || $user->daily_reset->lt(now()->startOfDay())) {
            $user->update([
                'search_count' => 0,
                'daily_reset' => now()->toDateString(),
            ]);
            return $searchLimit;
        }

        return max(0, $searchLimit - $user->search_count);
    }

    /**
     * Get user's search limit based on their subscription plan.
     */
    public function getUserSearchLimit(User $user): int
    {
        // Get user's active subscription
        $activeSubscription = $user->activeSubscription;

        if ($activeSubscription && $activeSubscription->pricingPlan) {
            return $activeSubscription->pricingPlan->search_limit ?? config('app.free_search_limit', 20);
        }

        // Fallback to free plan limit from config or default
        return config('app.free_search_limit', 20);
    }

    /**
     * Create a premium subscription for user.
     */
    public function createPremiumSubscription(?User $user, ?string $subscriptionId = null, string $paymentGateway = 'paddle'): Subscription
    {
        return DB::transaction(function () use ($user, $subscriptionId, $paymentGateway) {
            // Validate user
            if (!$user || !$user->exists) {
                throw new \InvalidArgumentException('Invalid user provided');
            }

            // Check if user already has an active subscription (only for external calls, not internal)
            $existingSubscription = $user->subscriptions()
                ->where('status', 'active')
                ->where('current_period_end', '>', now())
                ->first();

            if ($existingSubscription && !app()->runningInConsole() && !app()->environment('testing')) {
                throw new \Exception('User already has an active subscription');
            }

            // Get premium pricing plan with better error handling
            $premiumPlan = PricingPlan::where('name', 'premium')
                ->where('is_active', true)
                ->first();

            if (!$premiumPlan) {
                \Log::error('Premium plan not found', [
                    'user_id' => $user->id,
                    'available_plans' => PricingPlan::pluck('name', 'is_active')->toArray()
                ]);
                throw new \Exception('Premium plan not found or not active');
            }

            // Cancel any existing active subscriptions with proper logging
            $cancelledCount = $user->subscriptions()
                ->where('status', 'active')
                ->update(['status' => 'cancelled']);

            if ($cancelledCount > 0) {
                \Log::info('Cancelled existing subscriptions', [
                    'user_id' => $user->id,
                    'cancelled_count' => $cancelledCount
                ]);
            }

            // Prepare subscription data with validation
            $subscriptionData = [
                'plan_name' => 'premium',
                'pricing_plan_id' => $premiumPlan->id,
                'status' => 'active',
                'current_period_start' => now(),
                'current_period_end' => now()->addMonth(),
                'payment_gateway' => $paymentGateway,
            ];

            // Add gateway-specific subscription ID with validation
            if ($subscriptionId) {
                switch ($paymentGateway) {
                    case 'paddle':
                        $subscriptionData['paddle_subscription_id'] = $subscriptionId;
                        break;
                    case 'shurjopay':
                        $subscriptionData['shurjopay_subscription_id'] = $subscriptionId;
                        break;
                    case 'coinbase_commerce':
                        $subscriptionData['coinbase_commerce_subscription_id'] = $subscriptionId;
                        break;
                    default:
                        \Log::warning('Unknown payment gateway', [
                            'gateway' => $paymentGateway,
                            'user_id' => $user->id
                        ]);
                }
            }

            // Create new subscription with error handling
            try {
                $subscription = $user->subscriptions()->create($subscriptionData);
            } catch (\Exception $e) {
                \Log::error('Failed to create subscription record', [
                    'user_id' => $user->id,
                    'data' => $subscriptionData,
                    'error' => $e->getMessage()
                ]);
                throw new \Exception('Failed to create subscription: ' . $e->getMessage());
            }

            // Update user's subscription plan with error handling
            try {
                $user->update(['subscription_plan' => 'premium']);
            } catch (\Exception $e) {
                \Log::error('Failed to update user subscription plan', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'error' => $e->getMessage()
                ]);
                // Don't throw here as subscription is already created
            }

            \Log::info('Premium subscription created successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'plan_id' => $premiumPlan->id,
                'payment_gateway' => $paymentGateway,
                'period_start' => $subscription->current_period_start,
                'period_end' => $subscription->current_period_end
            ]);

            return $subscription;
        }, 5); // 5 second timeout for database transaction
    }

    /**
     * Cancel user's subscription.
     */
    public function cancelSubscription(User $user): bool
    {
        return DB::transaction(function () use ($user) {
            // Cancel all active subscriptions (handles edge case of multiple active subscriptions)
            $cancelledCount = $user->subscriptions()
                ->where('status', 'active')
                ->where('current_period_end', '>', now())
                ->update(['status' => 'cancelled']);

            if ($cancelledCount === 0) {
                return false;
            }

            $user->update(['subscription_plan' => 'free']);

            return true;
        });
    }

    /**
     * Get subscription plans configuration.
     */
    public function getPlans(): array
    {
        $plans = PricingPlan::active()->ordered()->get();

        $formattedPlans = [];
        foreach ($plans as $plan) {
            $formattedPlans[$plan->name] = [
                'id' => $plan->id,
                'name' => $plan->display_name,
                'price' => $plan->price,
                'currency' => $plan->currency,
                'interval' => $plan->interval,
                'features' => $plan->features ?? [],
                'search_limit' => $plan->search_limit,
                'is_popular' => $plan->is_popular,
                'formatted_price' => $plan->formatted_price,
                'metadata' => $plan->metadata ?? [],
            ];
        }

        // Fallback to hardcoded plans if no plans in database
        if (empty($formattedPlans)) {
            return [
                'free' => [
                    'name' => 'Free',
                    'price' => 0,
                    'currency' => 'USD',
                    'interval' => 'month',
                    'features' => [
                        '20 searches per day',
                        'Basic part information',
                        'Standard resolution images',
                        'Email support',
                    ],
                    'search_limit' => 20,
                ],
                'premium' => [
                    'name' => 'Premium',
                    'price' => 19,
                    'currency' => 'USD',
                    'interval' => 'month',
                    'features' => [
                        'Unlimited searches',
                        'Detailed specifications',
                        'High-resolution images',
                        'Priority support',
                        'Advanced filters',
                    ],
                    'search_limit' => -1, // Unlimited
                ],
            ];
        }

        return $formattedPlans;
    }

    /**
     * Create a subscription from a Paddle transaction.
     */
    public function createPaddleSubscription(User $user, \App\Models\PaddleTransaction $transaction, array $paddleData): Subscription
    {
        return DB::transaction(function () use ($user, $transaction, $paddleData) {
            // Validate inputs
            if (!$user || !$user->exists) {
                throw new \InvalidArgumentException('Invalid user provided');
            }

            if (!$transaction || !$transaction->exists) {
                throw new \InvalidArgumentException('Invalid transaction provided');
            }

            // Get the pricing plan from transaction items
            $items = $transaction->items;
            $planName = $items[0]['plan_name'] ?? 'premium';
            $billingCycle = $items[0]['billing_cycle'] ?? 'month';

            $pricingPlan = PricingPlan::where('name', $planName)->where('is_active', true)->first();
            if (!$pricingPlan) {
                \Log::error('Pricing plan not found for Paddle subscription', [
                    'plan_name' => $planName,
                    'user_id' => $user->id,
                    'transaction_id' => $transaction->id,
                    'available_plans' => PricingPlan::where('is_active', true)->pluck('name')->toArray()
                ]);
                throw new \Exception("Pricing plan '{$planName}' not found or not active");
            }

            // Cancel any existing active subscriptions
            $cancelledCount = $user->subscriptions()
                ->where('status', 'active')
                ->update(['status' => 'cancelled']);

            if ($cancelledCount > 0) {
                \Log::info('Cancelled existing subscriptions for Paddle payment', [
                    'user_id' => $user->id,
                    'cancelled_count' => $cancelledCount,
                    'transaction_id' => $transaction->id
                ]);
            }

            // Calculate subscription period
            $startDate = now();
            $endDate = $billingCycle === 'year' ? $startDate->copy()->addYear() : $startDate->copy()->addMonth();

            // Create new subscription
            $subscription = $user->subscriptions()->create([
                'plan_name' => $planName,
                'pricing_plan_id' => $pricingPlan->id,
                'status' => 'active',
                'current_period_start' => $startDate,
                'current_period_end' => $endDate,
                'paddle_subscription_id' => $paddleData['subscription_id'] ?? $transaction->paddle_transaction_id,
                'payment_gateway' => 'paddle',
            ]);

            // Update transaction with subscription reference
            $transaction->update(['subscription_id' => $subscription->id]);

            // Update user's subscription plan
            $user->update(['subscription_plan' => $planName]);

            \Log::info('Paddle subscription created successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'transaction_id' => $transaction->id,
                'plan_name' => $planName,
                'billing_cycle' => $billingCycle,
                'period_start' => $subscription->current_period_start,
                'period_end' => $subscription->current_period_end
            ]);

            return $subscription;
        }, 5); // 5 second timeout for database transaction
    }

    /**
     * Get a pricing plan by name.
     */
    public function getPricingPlanByName(string $name): ?PricingPlan
    {
        return PricingPlan::where('name', $name)->where('is_active', true)->first();
    }
}
