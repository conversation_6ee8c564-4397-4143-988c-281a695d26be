#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix imports in a test file
function fixTestImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file already imports from test-utils
    if (content.includes("from '../utils/test-utils'") || content.includes("from '../../utils/test-utils'")) {
      console.log(`Skipping ${filePath} - already uses test-utils`);
      return;
    }
    
    // Check if file imports from @testing-library/react
    if (!content.includes("from '@testing-library/react'")) {
      console.log(`Skipping ${filePath} - doesn't import from @testing-library/react`);
      return;
    }
    
    let newContent = content;
    
    // Calculate relative path to test-utils
    const relativePath = path.relative(path.dirname(filePath), 'tests/utils/test-utils');
    const importPath = relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
    
    // Replace @testing-library/react imports
    newContent = newContent.replace(
      /import\s*{([^}]+)}\s*from\s*'@testing-library\/react';?/g,
      (match, imports) => {
        // Clean up imports and add userEvent if needed
        const cleanImports = imports.split(',').map(imp => imp.trim()).filter(imp => imp);
        if (!cleanImports.includes('userEvent') && content.includes('userEvent')) {
          cleanImports.push('userEvent');
        }
        return `import { ${cleanImports.join(', ')} } from '${importPath}';`;
      }
    );
    
    // Remove separate userEvent import if it exists
    newContent = newContent.replace(
      /import\s+userEvent\s+from\s+'@testing-library\/user-event';?\n?/g,
      ''
    );
    
    // Write the updated content back to the file
    if (newContent !== content) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed imports in ${filePath}`);
    } else {
      console.log(`No changes needed for ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Get all test files that import from @testing-library/react
const { execSync } = require('child_process');

try {
  const testFiles = execSync('find tests -name "*.test.tsx" -o -name "*.test.ts" | xargs grep -l "@testing-library/react"', { encoding: 'utf8' })
    .trim()
    .split('\n')
    .filter(file => file.trim());
  
  console.log(`Found ${testFiles.length} test files to fix`);
  
  testFiles.forEach(fixTestImports);
  
  console.log('Done fixing test imports!');
} catch (error) {
  console.error('Error finding test files:', error.message);
}
